{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Paper, TextField, Button, Typography, Box, Alert, InputAdornment, IconButton, Link, Grid } from '@mui/material';\nimport { Visibility, VisibilityOff, PersonAdd, ArrowBack } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Register = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    username: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  const {\n    register\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleInputChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear error for this field when user starts typing\n    if (errors[e.target.name]) {\n      setErrors({\n        ...errors,\n        [e.target.name]: ''\n      });\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Name validation\n    if (!formData.name.trim()) {\n      newErrors.name = 'Full name is required';\n    }\n\n    // Username validation\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    } else if (formData.username.length < 3) {\n      newErrors.username = 'Username must be at least 3 characters';\n    }\n\n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!emailRegex.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Phone validation\n    const phoneRegex = /^[\\d\\s\\-()\\\\+]+$/;\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    } else if (!phoneRegex.test(formData.phone)) {\n      newErrors.phone = 'Please enter a valid phone number';\n    }\n\n    // Password validation\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    // Confirm password validation\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    setLoading(true);\n    const result = await register({\n      name: formData.name.trim(),\n      username: formData.username.trim(),\n      email: formData.email.trim(),\n      phone: formData.phone.trim(),\n      password: formData.password\n    });\n    if (result.success) {\n      navigate('/appointments');\n    } else {\n      setErrors({\n        submit: result.error\n      });\n    }\n    setLoading(false);\n  };\n  const handleBackToLogin = () => {\n    navigate('/login');\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    component: \"main\",\n    maxWidth: \"sm\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        marginTop: 8,\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        elevation: 3,\n        sx: {\n          padding: 4,\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleBackToLogin,\n            sx: {\n              mr: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            component: \"h1\",\n            variant: \"h4\",\n            sx: {\n              flexGrow: 1,\n              textAlign: 'center'\n            },\n            children: \"Customer Registration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'center',\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(PersonAdd, {\n            sx: {\n              fontSize: 40,\n              color: 'primary.main'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          component: \"form\",\n          onSubmit: handleSubmit,\n          sx: {\n            mt: 1\n          },\n          children: [errors.submit && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: errors.submit\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                required: true,\n                fullWidth: true,\n                id: \"name\",\n                label: \"Full Name\",\n                name: \"name\",\n                autoComplete: \"name\",\n                autoFocus: true,\n                value: formData.name,\n                onChange: handleInputChange,\n                error: !!errors.name,\n                helperText: errors.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                required: true,\n                fullWidth: true,\n                id: \"username\",\n                label: \"Username\",\n                name: \"username\",\n                autoComplete: \"username\",\n                value: formData.username,\n                onChange: handleInputChange,\n                error: !!errors.username,\n                helperText: errors.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              sm: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                required: true,\n                fullWidth: true,\n                id: \"phone\",\n                label: \"Phone Number\",\n                name: \"phone\",\n                autoComplete: \"tel\",\n                value: formData.phone,\n                onChange: handleInputChange,\n                error: !!errors.phone,\n                helperText: errors.phone\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                required: true,\n                fullWidth: true,\n                id: \"email\",\n                label: \"Email Address\",\n                name: \"email\",\n                autoComplete: \"email\",\n                value: formData.email,\n                onChange: handleInputChange,\n                error: !!errors.email,\n                helperText: errors.email\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                required: true,\n                fullWidth: true,\n                name: \"password\",\n                label: \"Password\",\n                type: showPassword ? 'text' : 'password',\n                id: \"password\",\n                autoComplete: \"new-password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                error: !!errors.password,\n                helperText: errors.password,\n                InputProps: {\n                  endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      \"aria-label\": \"toggle password visibility\",\n                      onClick: () => setShowPassword(!showPassword),\n                      edge: \"end\",\n                      children: showPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 43\n                      }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 63\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                required: true,\n                fullWidth: true,\n                name: \"confirmPassword\",\n                label: \"Confirm Password\",\n                type: showConfirmPassword ? 'text' : 'password',\n                id: \"confirmPassword\",\n                value: formData.confirmPassword,\n                onChange: handleInputChange,\n                error: !!errors.confirmPassword,\n                helperText: errors.confirmPassword,\n                InputProps: {\n                  endAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                    position: \"end\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      \"aria-label\": \"toggle password visibility\",\n                      onClick: () => setShowConfirmPassword(!showConfirmPassword),\n                      edge: \"end\",\n                      children: showConfirmPassword ? /*#__PURE__*/_jsxDEV(VisibilityOff, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 50\n                      }, this) : /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 276,\n                        columnNumber: 70\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this)\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"submit\",\n            fullWidth: true,\n            variant: \"contained\",\n            sx: {\n              mt: 3,\n              mb: 2\n            },\n            disabled: loading,\n            children: loading ? 'Creating Account...' : 'Create Account'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n                component: \"button\",\n                variant: \"body2\",\n                onClick: handleBackToLogin,\n                sx: {\n                  textDecoration: 'none'\n                },\n                children: \"Sign in here\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this);\n};\n_s(Register, \"3FEtJBW9zCTuxWmz1KZR/YWCRi0=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = Register;\nexport default Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Paper", "TextField", "<PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "InputAdornment", "IconButton", "Link", "Grid", "Visibility", "VisibilityOff", "PersonAdd", "ArrowBack", "useAuth", "useNavigate", "jsxDEV", "_jsxDEV", "Register", "_s", "formData", "setFormData", "name", "username", "email", "phone", "password", "confirmPassword", "showPassword", "setShowPassword", "showConfirmPassword", "setShowConfirmPassword", "errors", "setErrors", "loading", "setLoading", "register", "navigate", "handleInputChange", "e", "target", "value", "validateForm", "newErrors", "trim", "length", "emailRegex", "test", "phoneRegex", "Object", "keys", "handleSubmit", "preventDefault", "result", "success", "submit", "error", "handleBackToLogin", "component", "max<PERSON><PERSON><PERSON>", "children", "sx", "marginTop", "display", "flexDirection", "alignItems", "elevation", "padding", "width", "mb", "onClick", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "flexGrow", "textAlign", "justifyContent", "fontSize", "color", "onSubmit", "mt", "severity", "container", "spacing", "item", "xs", "required", "fullWidth", "id", "label", "autoComplete", "autoFocus", "onChange", "helperText", "sm", "type", "InputProps", "endAdornment", "position", "edge", "disabled", "textDecoration", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Paper,\n  TextField,\n  Button,\n  Typography,\n  Box,\n  Alert,\n  InputAdornment,\n  IconButton,\n  Link,\n  Grid\n} from '@mui/material';\nimport {\n  Visibility,\n  VisibilityOff,\n  PersonAdd,\n  ArrowBack\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useNavigate } from 'react-router-dom';\n\nconst Register = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    username: '',\n    email: '',\n    phone: '',\n    password: '',\n    confirmPassword: ''\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\n  const [errors, setErrors] = useState({});\n  const [loading, setLoading] = useState(false);\n  \n  const { register } = useAuth();\n  const navigate = useNavigate();\n\n  const handleInputChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear error for this field when user starts typing\n    if (errors[e.target.name]) {\n      setErrors({\n        ...errors,\n        [e.target.name]: ''\n      });\n    }\n  };\n\n  const validateForm = () => {\n    const newErrors = {};\n\n    // Name validation\n    if (!formData.name.trim()) {\n      newErrors.name = 'Full name is required';\n    }\n\n    // Username validation\n    if (!formData.username.trim()) {\n      newErrors.username = 'Username is required';\n    } else if (formData.username.length < 3) {\n      newErrors.username = 'Username must be at least 3 characters';\n    }\n\n    // Email validation\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!emailRegex.test(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    // Phone validation\n    const phoneRegex = /^[\\d\\s\\-()\\\\+]+$/;\n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    } else if (!phoneRegex.test(formData.phone)) {\n      newErrors.phone = 'Please enter a valid phone number';\n    }\n\n    // Password validation\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 6) {\n      newErrors.password = 'Password must be at least 6 characters';\n    }\n\n    // Confirm password validation\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n\n    setLoading(true);\n\n    const result = await register({\n      name: formData.name.trim(),\n      username: formData.username.trim(),\n      email: formData.email.trim(),\n      phone: formData.phone.trim(),\n      password: formData.password\n    });\n    \n    if (result.success) {\n      navigate('/appointments');\n    } else {\n      setErrors({ submit: result.error });\n    }\n    \n    setLoading(false);\n  };\n\n  const handleBackToLogin = () => {\n    navigate('/login');\n  };\n\n  return (\n    <Container component=\"main\" maxWidth=\"sm\">\n      <Box\n        sx={{\n          marginTop: 8,\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n        }}\n      >\n        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <IconButton onClick={handleBackToLogin} sx={{ mr: 1 }}>\n              <ArrowBack />\n            </IconButton>\n            <Typography component=\"h1\" variant=\"h4\" sx={{ flexGrow: 1, textAlign: 'center' }}>\n              Customer Registration\n            </Typography>\n          </Box>\n\n          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>\n            <PersonAdd sx={{ fontSize: 40, color: 'primary.main' }} />\n          </Box>\n\n          <Box component=\"form\" onSubmit={handleSubmit} sx={{ mt: 1 }}>\n            {errors.submit && (\n              <Alert severity=\"error\" sx={{ mb: 2 }}>\n                {errors.submit}\n              </Alert>\n            )}\n\n            <Grid container spacing={2}>\n              <Grid item xs={12}>\n                <TextField\n                  required\n                  fullWidth\n                  id=\"name\"\n                  label=\"Full Name\"\n                  name=\"name\"\n                  autoComplete=\"name\"\n                  autoFocus\n                  value={formData.name}\n                  onChange={handleInputChange}\n                  error={!!errors.name}\n                  helperText={errors.name}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  id=\"username\"\n                  label=\"Username\"\n                  name=\"username\"\n                  autoComplete=\"username\"\n                  value={formData.username}\n                  onChange={handleInputChange}\n                  error={!!errors.username}\n                  helperText={errors.username}\n                />\n              </Grid>\n\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  id=\"phone\"\n                  label=\"Phone Number\"\n                  name=\"phone\"\n                  autoComplete=\"tel\"\n                  value={formData.phone}\n                  onChange={handleInputChange}\n                  error={!!errors.phone}\n                  helperText={errors.phone}\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  required\n                  fullWidth\n                  id=\"email\"\n                  label=\"Email Address\"\n                  name=\"email\"\n                  autoComplete=\"email\"\n                  value={formData.email}\n                  onChange={handleInputChange}\n                  error={!!errors.email}\n                  helperText={errors.email}\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  required\n                  fullWidth\n                  name=\"password\"\n                  label=\"Password\"\n                  type={showPassword ? 'text' : 'password'}\n                  id=\"password\"\n                  autoComplete=\"new-password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  error={!!errors.password}\n                  helperText={errors.password}\n                  InputProps={{\n                    endAdornment: (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          aria-label=\"toggle password visibility\"\n                          onClick={() => setShowPassword(!showPassword)}\n                          edge=\"end\"\n                        >\n                          {showPassword ? <VisibilityOff /> : <Visibility />}\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n              </Grid>\n\n              <Grid item xs={12}>\n                <TextField\n                  required\n                  fullWidth\n                  name=\"confirmPassword\"\n                  label=\"Confirm Password\"\n                  type={showConfirmPassword ? 'text' : 'password'}\n                  id=\"confirmPassword\"\n                  value={formData.confirmPassword}\n                  onChange={handleInputChange}\n                  error={!!errors.confirmPassword}\n                  helperText={errors.confirmPassword}\n                  InputProps={{\n                    endAdornment: (\n                      <InputAdornment position=\"end\">\n                        <IconButton\n                          aria-label=\"toggle password visibility\"\n                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}\n                          edge=\"end\"\n                        >\n                          {showConfirmPassword ? <VisibilityOff /> : <Visibility />}\n                        </IconButton>\n                      </InputAdornment>\n                    ),\n                  }}\n                />\n              </Grid>\n            </Grid>\n\n            <Button\n              type=\"submit\"\n              fullWidth\n              variant=\"contained\"\n              sx={{ mt: 3, mb: 2 }}\n              disabled={loading}\n            >\n              {loading ? 'Creating Account...' : 'Create Account'}\n            </Button>\n\n            <Box sx={{ textAlign: 'center' }}>\n              <Typography variant=\"body2\">\n                Already have an account?{' '}\n                <Link\n                  component=\"button\"\n                  variant=\"body2\"\n                  onClick={handleBackToLogin}\n                  sx={{ textDecoration: 'none' }}\n                >\n                  Sign in here\n                </Link>\n              </Typography>\n            </Box>\n          </Box>\n        </Paper>\n      </Box>\n    </Container>\n  );\n};\n\nexport default Register;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,UAAU,EACVC,GAAG,EACHC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,IAAI,EACJC,IAAI,QACC,eAAe;AACtB,SACEC,UAAU,EACVC,aAAa,EACbC,SAAS,EACTC,SAAS,QACJ,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC;IACvCwB,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACgC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACkC,MAAM,EAAEC,SAAS,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM;IAAEsC;EAAS,CAAC,GAAGtB,OAAO,CAAC,CAAC;EAC9B,MAAMuB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAMuB,iBAAiB,GAAIC,CAAC,IAAK;IAC/BlB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACmB,CAAC,CAACC,MAAM,CAAClB,IAAI,GAAGiB,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;IACF;IACA,IAAIT,MAAM,CAACO,CAAC,CAACC,MAAM,CAAClB,IAAI,CAAC,EAAE;MACzBW,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACO,CAAC,CAACC,MAAM,CAAClB,IAAI,GAAG;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;;IAEpB;IACA,IAAI,CAACvB,QAAQ,CAACE,IAAI,CAACsB,IAAI,CAAC,CAAC,EAAE;MACzBD,SAAS,CAACrB,IAAI,GAAG,uBAAuB;IAC1C;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,QAAQ,CAACqB,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAACpB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIH,QAAQ,CAACG,QAAQ,CAACsB,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACpB,QAAQ,GAAG,wCAAwC;IAC/D;;IAEA;IACA,MAAMuB,UAAU,GAAG,4BAA4B;IAC/C,IAAI,CAAC1B,QAAQ,CAACI,KAAK,CAACoB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAACnB,KAAK,GAAG,mBAAmB;IACvC,CAAC,MAAM,IAAI,CAACsB,UAAU,CAACC,IAAI,CAAC3B,QAAQ,CAACI,KAAK,CAAC,EAAE;MAC3CmB,SAAS,CAACnB,KAAK,GAAG,oCAAoC;IACxD;;IAEA;IACA,MAAMwB,UAAU,GAAG,kBAAkB;IACrC,IAAI,CAAC5B,QAAQ,CAACK,KAAK,CAACmB,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAClB,KAAK,GAAG,0BAA0B;IAC9C,CAAC,MAAM,IAAI,CAACuB,UAAU,CAACD,IAAI,CAAC3B,QAAQ,CAACK,KAAK,CAAC,EAAE;MAC3CkB,SAAS,CAAClB,KAAK,GAAG,mCAAmC;IACvD;;IAEA;IACA,IAAI,CAACL,QAAQ,CAACM,QAAQ,EAAE;MACtBiB,SAAS,CAACjB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIN,QAAQ,CAACM,QAAQ,CAACmB,MAAM,GAAG,CAAC,EAAE;MACvCF,SAAS,CAACjB,QAAQ,GAAG,wCAAwC;IAC/D;;IAEA;IACA,IAAI,CAACN,QAAQ,CAACO,eAAe,EAAE;MAC7BgB,SAAS,CAAChB,eAAe,GAAG,8BAA8B;IAC5D,CAAC,MAAM,IAAIP,QAAQ,CAACM,QAAQ,KAAKN,QAAQ,CAACO,eAAe,EAAE;MACzDgB,SAAS,CAAChB,eAAe,GAAG,wBAAwB;IACtD;IAEAM,SAAS,CAACU,SAAS,CAAC;IACpB,OAAOM,MAAM,CAACC,IAAI,CAACP,SAAS,CAAC,CAACE,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMM,YAAY,GAAG,MAAOZ,CAAC,IAAK;IAChCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACV,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEAP,UAAU,CAAC,IAAI,CAAC;IAEhB,MAAMkB,MAAM,GAAG,MAAMjB,QAAQ,CAAC;MAC5Bd,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAACsB,IAAI,CAAC,CAAC;MAC1BrB,QAAQ,EAAEH,QAAQ,CAACG,QAAQ,CAACqB,IAAI,CAAC,CAAC;MAClCpB,KAAK,EAAEJ,QAAQ,CAACI,KAAK,CAACoB,IAAI,CAAC,CAAC;MAC5BnB,KAAK,EAAEL,QAAQ,CAACK,KAAK,CAACmB,IAAI,CAAC,CAAC;MAC5BlB,QAAQ,EAAEN,QAAQ,CAACM;IACrB,CAAC,CAAC;IAEF,IAAI2B,MAAM,CAACC,OAAO,EAAE;MAClBjB,QAAQ,CAAC,eAAe,CAAC;IAC3B,CAAC,MAAM;MACLJ,SAAS,CAAC;QAAEsB,MAAM,EAAEF,MAAM,CAACG;MAAM,CAAC,CAAC;IACrC;IAEArB,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpB,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,oBACEpB,OAAA,CAAClB,SAAS;IAAC2D,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACvC3C,OAAA,CAACb,GAAG;MACFyD,EAAE,EAAE;QACFC,SAAS,EAAE,CAAC;QACZC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,eAEF3C,OAAA,CAACjB,KAAK;QAACkE,SAAS,EAAE,CAAE;QAACL,EAAE,EAAE;UAAEM,OAAO,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAR,QAAA,gBACrD3C,OAAA,CAACb,GAAG;UAACyD,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxD3C,OAAA,CAACV,UAAU;YAAC+D,OAAO,EAAEb,iBAAkB;YAACI,EAAE,EAAE;cAAEU,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,eACpD3C,OAAA,CAACJ,SAAS;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACb1D,OAAA,CAACd,UAAU;YAACuD,SAAS,EAAC,IAAI;YAACkB,OAAO,EAAC,IAAI;YAACf,EAAE,EAAE;cAAEgB,QAAQ,EAAE,CAAC;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAlB,QAAA,EAAC;UAElF;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAEN1D,OAAA,CAACb,GAAG;UAACyD,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEgB,cAAc,EAAE,QAAQ;YAAEV,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,eAC5D3C,OAAA,CAACL,SAAS;YAACiD,EAAE,EAAE;cAAEmB,QAAQ,EAAE,EAAE;cAAEC,KAAK,EAAE;YAAe;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAEN1D,OAAA,CAACb,GAAG;UAACsD,SAAS,EAAC,MAAM;UAACwB,QAAQ,EAAE/B,YAAa;UAACU,EAAE,EAAE;YAAEsB,EAAE,EAAE;UAAE,CAAE;UAAAvB,QAAA,GACzD5B,MAAM,CAACuB,MAAM,iBACZtC,OAAA,CAACZ,KAAK;YAAC+E,QAAQ,EAAC,OAAO;YAACvB,EAAE,EAAE;cAAEQ,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,EACnC5B,MAAM,CAACuB;UAAM;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CACR,eAED1D,OAAA,CAACR,IAAI;YAAC4E,SAAS;YAACC,OAAO,EAAE,CAAE;YAAA1B,QAAA,gBACzB3C,OAAA,CAACR,IAAI;cAAC8E,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA5B,QAAA,eAChB3C,OAAA,CAAChB,SAAS;gBACRwF,QAAQ;gBACRC,SAAS;gBACTC,EAAE,EAAC,MAAM;gBACTC,KAAK,EAAC,WAAW;gBACjBtE,IAAI,EAAC,MAAM;gBACXuE,YAAY,EAAC,MAAM;gBACnBC,SAAS;gBACTrD,KAAK,EAAErB,QAAQ,CAACE,IAAK;gBACrByE,QAAQ,EAAEzD,iBAAkB;gBAC5BkB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACV,IAAK;gBACrB0E,UAAU,EAAEhE,MAAM,CAACV;cAAK;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1D,OAAA,CAACR,IAAI;cAAC8E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACS,EAAE,EAAE,CAAE;cAAArC,QAAA,eACvB3C,OAAA,CAAChB,SAAS;gBACRwF,QAAQ;gBACRC,SAAS;gBACTC,EAAE,EAAC,UAAU;gBACbC,KAAK,EAAC,UAAU;gBAChBtE,IAAI,EAAC,UAAU;gBACfuE,YAAY,EAAC,UAAU;gBACvBpD,KAAK,EAAErB,QAAQ,CAACG,QAAS;gBACzBwE,QAAQ,EAAEzD,iBAAkB;gBAC5BkB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACT,QAAS;gBACzByE,UAAU,EAAEhE,MAAM,CAACT;cAAS;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1D,OAAA,CAACR,IAAI;cAAC8E,IAAI;cAACC,EAAE,EAAE,EAAG;cAACS,EAAE,EAAE,CAAE;cAAArC,QAAA,eACvB3C,OAAA,CAAChB,SAAS;gBACRwF,QAAQ;gBACRC,SAAS;gBACTC,EAAE,EAAC,OAAO;gBACVC,KAAK,EAAC,cAAc;gBACpBtE,IAAI,EAAC,OAAO;gBACZuE,YAAY,EAAC,KAAK;gBAClBpD,KAAK,EAAErB,QAAQ,CAACK,KAAM;gBACtBsE,QAAQ,EAAEzD,iBAAkB;gBAC5BkB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACP,KAAM;gBACtBuE,UAAU,EAAEhE,MAAM,CAACP;cAAM;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1D,OAAA,CAACR,IAAI;cAAC8E,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA5B,QAAA,eAChB3C,OAAA,CAAChB,SAAS;gBACRwF,QAAQ;gBACRC,SAAS;gBACTC,EAAE,EAAC,OAAO;gBACVC,KAAK,EAAC,eAAe;gBACrBtE,IAAI,EAAC,OAAO;gBACZuE,YAAY,EAAC,OAAO;gBACpBpD,KAAK,EAAErB,QAAQ,CAACI,KAAM;gBACtBuE,QAAQ,EAAEzD,iBAAkB;gBAC5BkB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACR,KAAM;gBACtBwE,UAAU,EAAEhE,MAAM,CAACR;cAAM;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1D,OAAA,CAACR,IAAI;cAAC8E,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA5B,QAAA,eAChB3C,OAAA,CAAChB,SAAS;gBACRwF,QAAQ;gBACRC,SAAS;gBACTpE,IAAI,EAAC,UAAU;gBACfsE,KAAK,EAAC,UAAU;gBAChBM,IAAI,EAAEtE,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzC+D,EAAE,EAAC,UAAU;gBACbE,YAAY,EAAC,cAAc;gBAC3BpD,KAAK,EAAErB,QAAQ,CAACM,QAAS;gBACzBqE,QAAQ,EAAEzD,iBAAkB;gBAC5BkB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACN,QAAS;gBACzBsE,UAAU,EAAEhE,MAAM,CAACN,QAAS;gBAC5ByE,UAAU,EAAE;kBACVC,YAAY,eACVnF,OAAA,CAACX,cAAc;oBAAC+F,QAAQ,EAAC,KAAK;oBAAAzC,QAAA,eAC5B3C,OAAA,CAACV,UAAU;sBACT,cAAW,4BAA4B;sBACvC+D,OAAO,EAAEA,CAAA,KAAMzC,eAAe,CAAC,CAACD,YAAY,CAAE;sBAC9C0E,IAAI,EAAC,KAAK;sBAAA1C,QAAA,EAEThC,YAAY,gBAAGX,OAAA,CAACN,aAAa;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAG1D,OAAA,CAACP,UAAU;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEP1D,OAAA,CAACR,IAAI;cAAC8E,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA5B,QAAA,eAChB3C,OAAA,CAAChB,SAAS;gBACRwF,QAAQ;gBACRC,SAAS;gBACTpE,IAAI,EAAC,iBAAiB;gBACtBsE,KAAK,EAAC,kBAAkB;gBACxBM,IAAI,EAAEpE,mBAAmB,GAAG,MAAM,GAAG,UAAW;gBAChD6D,EAAE,EAAC,iBAAiB;gBACpBlD,KAAK,EAAErB,QAAQ,CAACO,eAAgB;gBAChCoE,QAAQ,EAAEzD,iBAAkB;gBAC5BkB,KAAK,EAAE,CAAC,CAACxB,MAAM,CAACL,eAAgB;gBAChCqE,UAAU,EAAEhE,MAAM,CAACL,eAAgB;gBACnCwE,UAAU,EAAE;kBACVC,YAAY,eACVnF,OAAA,CAACX,cAAc;oBAAC+F,QAAQ,EAAC,KAAK;oBAAAzC,QAAA,eAC5B3C,OAAA,CAACV,UAAU;sBACT,cAAW,4BAA4B;sBACvC+D,OAAO,EAAEA,CAAA,KAAMvC,sBAAsB,CAAC,CAACD,mBAAmB,CAAE;sBAC5DwE,IAAI,EAAC,KAAK;sBAAA1C,QAAA,EAET9B,mBAAmB,gBAAGb,OAAA,CAACN,aAAa;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAAG1D,OAAA,CAACP,UAAU;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAEpB;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEP1D,OAAA,CAACf,MAAM;YACLgG,IAAI,EAAC,QAAQ;YACbR,SAAS;YACTd,OAAO,EAAC,WAAW;YACnBf,EAAE,EAAE;cAAEsB,EAAE,EAAE,CAAC;cAAEd,EAAE,EAAE;YAAE,CAAE;YACrBkC,QAAQ,EAAErE,OAAQ;YAAA0B,QAAA,EAEjB1B,OAAO,GAAG,qBAAqB,GAAG;UAAgB;YAAAsC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eAET1D,OAAA,CAACb,GAAG;YAACyD,EAAE,EAAE;cAAEiB,SAAS,EAAE;YAAS,CAAE;YAAAlB,QAAA,eAC/B3C,OAAA,CAACd,UAAU;cAACyE,OAAO,EAAC,OAAO;cAAAhB,QAAA,GAAC,0BACF,EAAC,GAAG,eAC5B3C,OAAA,CAACT,IAAI;gBACHkD,SAAS,EAAC,QAAQ;gBAClBkB,OAAO,EAAC,OAAO;gBACfN,OAAO,EAAEb,iBAAkB;gBAC3BI,EAAE,EAAE;kBAAE2C,cAAc,EAAE;gBAAO,CAAE;gBAAA5C,QAAA,EAChC;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACxD,EAAA,CAjSID,QAAQ;EAAA,QAcSJ,OAAO,EACXC,WAAW;AAAA;AAAA0F,EAAA,GAfxBvF,QAAQ;AAmSd,eAAeA,QAAQ;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}