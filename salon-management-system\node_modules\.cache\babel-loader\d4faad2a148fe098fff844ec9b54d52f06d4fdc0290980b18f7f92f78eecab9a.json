{"ast": null, "code": "var getMapData = require('./_getMapData');\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\nmodule.exports = mapCacheHas;", "map": {"version": 3, "names": ["getMapData", "require", "mapCacheHas", "key", "has", "module", "exports"], "sources": ["D:/Project/salon-management-system/node_modules/lodash/_mapCacheHas.js"], "sourcesContent": ["var getMapData = require('./_getMapData');\n\n/**\n * Checks if a map value for `key` exists.\n *\n * @private\n * @name has\n * @memberOf MapCache\n * @param {string} key The key of the entry to check.\n * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.\n */\nfunction mapCacheHas(key) {\n  return getMapData(this, key).has(key);\n}\n\nmodule.exports = mapCacheHas;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,GAAG,EAAE;EACxB,OAAOH,UAAU,CAAC,IAAI,EAAEG,GAAG,CAAC,CAACC,GAAG,CAACD,GAAG,CAAC;AACvC;AAEAE,MAAM,CAACC,OAAO,GAAGJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}