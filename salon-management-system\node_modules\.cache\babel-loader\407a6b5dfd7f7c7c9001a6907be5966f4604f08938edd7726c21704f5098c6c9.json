{"ast": null, "code": "'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n    className,\n    elementType: initialElementType,\n    ownerState,\n    externalForwardedProps,\n    internalForwardedProps,\n    shouldForwardComponentProp = false,\n    ...useSlotPropsParams\n  } = parameters;\n  const {\n    component: rootComponent,\n    slots = {\n      [name]: undefined\n    },\n    slotProps = {\n      [name]: undefined\n    },\n    ...other\n  } = externalForwardedProps;\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const {\n    props: {\n      component: slotComponent,\n      ...mergedProps\n    },\n    internalRef\n  } = mergeSlotProps({\n    className,\n    ...useSlotPropsParams,\n    externalForwardedProps: name === 'root' ? other : undefined,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.ref);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, {\n    ...(name === 'root' && !rootComponent && !slots[name] && internalForwardedProps),\n    ...(name !== 'root' && !slots[name] && internalForwardedProps),\n    ...mergedProps,\n    ...(LeafComponent && !shouldForwardComponentProp && {\n      as: LeafComponent\n    }),\n    ...(LeafComponent && shouldForwardComponentProp && {\n      component: LeafComponent\n    }),\n    ref\n  }, ownerState);\n  return [elementType, props];\n}", "map": {"version": 3, "names": ["useForkRef", "appendOwnerState", "resolveComponentProps", "mergeSlotProps", "useSlot", "name", "parameters", "className", "elementType", "initialElementType", "ownerState", "externalForwardedProps", "internalForwardedProps", "shouldForwardComponentProp", "useSlotPropsParams", "component", "rootComponent", "slots", "undefined", "slotProps", "other", "resolvedComponentsProps", "props", "slotComponent", "mergedProps", "internalRef", "externalSlotProps", "ref", "LeafComponent", "as"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/utils/useSlot.js"], "sourcesContent": ["'use client';\n\nimport useForkRef from '@mui/utils/useForkRef';\nimport appendOwnerState from '@mui/utils/appendOwnerState';\nimport resolveComponentProps from '@mui/utils/resolveComponentProps';\nimport mergeSlotProps from '@mui/utils/mergeSlotProps';\n/**\n * An internal function to create a Material UI slot.\n *\n * This is an advanced version of Base UI `useSlotProps` because Material UI allows leaf component to be customized via `component` prop\n * while Base UI does not need to support leaf component customization.\n *\n * @param {string} name: name of the slot\n * @param {object} parameters\n * @returns {[Slot, slotProps]} The slot's React component and the slot's props\n *\n * Note: the returned slot's props\n * - will never contain `component` prop.\n * - might contain `as` prop.\n */\nexport default function useSlot(\n/**\n * The slot's name. All Material UI components should have `root` slot.\n *\n * If the name is `root`, the logic behaves differently from other slots,\n * e.g. the `externalForwardedProps` are spread to `root` slot but not other slots.\n */\nname, parameters) {\n  const {\n    className,\n    elementType: initialElementType,\n    ownerState,\n    externalForwardedProps,\n    internalForwardedProps,\n    shouldForwardComponentProp = false,\n    ...useSlotPropsParams\n  } = parameters;\n  const {\n    component: rootComponent,\n    slots = {\n      [name]: undefined\n    },\n    slotProps = {\n      [name]: undefined\n    },\n    ...other\n  } = externalForwardedProps;\n  const elementType = slots[name] || initialElementType;\n\n  // `slotProps[name]` can be a callback that receives the component's ownerState.\n  // `resolvedComponentsProps` is always a plain object.\n  const resolvedComponentsProps = resolveComponentProps(slotProps[name], ownerState);\n  const {\n    props: {\n      component: slotComponent,\n      ...mergedProps\n    },\n    internalRef\n  } = mergeSlotProps({\n    className,\n    ...useSlotPropsParams,\n    externalForwardedProps: name === 'root' ? other : undefined,\n    externalSlotProps: resolvedComponentsProps\n  });\n  const ref = useForkRef(internalRef, resolvedComponentsProps?.ref, parameters.ref);\n  const LeafComponent = name === 'root' ? slotComponent || rootComponent : slotComponent;\n  const props = appendOwnerState(elementType, {\n    ...(name === 'root' && !rootComponent && !slots[name] && internalForwardedProps),\n    ...(name !== 'root' && !slots[name] && internalForwardedProps),\n    ...mergedProps,\n    ...(LeafComponent && !shouldForwardComponentProp && {\n      as: LeafComponent\n    }),\n    ...(LeafComponent && shouldForwardComponentProp && {\n      component: LeafComponent\n    }),\n    ref\n  }, ownerState);\n  return [elementType, props];\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAOC,qBAAqB,MAAM,kCAAkC;AACpE,OAAOC,cAAc,MAAM,2BAA2B;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA;AAC/B;AACA;AACA;AACA;AACA;AACA;AACAC,IAAI,EAAEC,UAAU,EAAE;EAChB,MAAM;IACJC,SAAS;IACTC,WAAW,EAAEC,kBAAkB;IAC/BC,UAAU;IACVC,sBAAsB;IACtBC,sBAAsB;IACtBC,0BAA0B,GAAG,KAAK;IAClC,GAAGC;EACL,CAAC,GAAGR,UAAU;EACd,MAAM;IACJS,SAAS,EAAEC,aAAa;IACxBC,KAAK,GAAG;MACN,CAACZ,IAAI,GAAGa;IACV,CAAC;IACDC,SAAS,GAAG;MACV,CAACd,IAAI,GAAGa;IACV,CAAC;IACD,GAAGE;EACL,CAAC,GAAGT,sBAAsB;EAC1B,MAAMH,WAAW,GAAGS,KAAK,CAACZ,IAAI,CAAC,IAAII,kBAAkB;;EAErD;EACA;EACA,MAAMY,uBAAuB,GAAGnB,qBAAqB,CAACiB,SAAS,CAACd,IAAI,CAAC,EAAEK,UAAU,CAAC;EAClF,MAAM;IACJY,KAAK,EAAE;MACLP,SAAS,EAAEQ,aAAa;MACxB,GAAGC;IACL,CAAC;IACDC;EACF,CAAC,GAAGtB,cAAc,CAAC;IACjBI,SAAS;IACT,GAAGO,kBAAkB;IACrBH,sBAAsB,EAAEN,IAAI,KAAK,MAAM,GAAGe,KAAK,GAAGF,SAAS;IAC3DQ,iBAAiB,EAAEL;EACrB,CAAC,CAAC;EACF,MAAMM,GAAG,GAAG3B,UAAU,CAACyB,WAAW,EAAEJ,uBAAuB,EAAEM,GAAG,EAAErB,UAAU,CAACqB,GAAG,CAAC;EACjF,MAAMC,aAAa,GAAGvB,IAAI,KAAK,MAAM,GAAGkB,aAAa,IAAIP,aAAa,GAAGO,aAAa;EACtF,MAAMD,KAAK,GAAGrB,gBAAgB,CAACO,WAAW,EAAE;IAC1C,IAAIH,IAAI,KAAK,MAAM,IAAI,CAACW,aAAa,IAAI,CAACC,KAAK,CAACZ,IAAI,CAAC,IAAIO,sBAAsB,CAAC;IAChF,IAAIP,IAAI,KAAK,MAAM,IAAI,CAACY,KAAK,CAACZ,IAAI,CAAC,IAAIO,sBAAsB,CAAC;IAC9D,GAAGY,WAAW;IACd,IAAII,aAAa,IAAI,CAACf,0BAA0B,IAAI;MAClDgB,EAAE,EAAED;IACN,CAAC,CAAC;IACF,IAAIA,aAAa,IAAIf,0BAA0B,IAAI;MACjDE,SAAS,EAAEa;IACb,CAAC,CAAC;IACFD;EACF,CAAC,EAAEjB,UAAU,CAAC;EACd,OAAO,CAACF,WAAW,EAAEc,KAAK,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}