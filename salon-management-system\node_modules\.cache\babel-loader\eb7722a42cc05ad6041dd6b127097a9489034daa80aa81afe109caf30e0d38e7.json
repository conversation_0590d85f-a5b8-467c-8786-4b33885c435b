{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, Button, Box, useTheme, useMediaQuery } from '@mui/material';\nimport { ContentCut as SalonIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const menuItems = [{\n    text: 'Dashboard',\n    path: '/'\n  }, {\n    text: 'Appointments',\n    path: '/appointments'\n  }, {\n    text: 'Customers',\n    path: '/customers'\n  }, {\n    text: 'Services',\n    path: '/services'\n  }, {\n    text: 'Staff',\n    path: '/staff'\n  }, {\n    text: 'Reports',\n    path: '/reports'\n  }];\n  const handleNavigation = path => {\n    navigate(path);\n  };\n  return /*#__PURE__*/_jsxDEV(AppBar, {\n    position: \"fixed\",\n    sx: {\n      zIndex: 1000\n    },\n    children: /*#__PURE__*/_jsxDEV(Toolbar, {\n      sx: {\n        minHeight: '56px !important'\n      },\n      children: [/*#__PURE__*/_jsxDEV(SalonIcon, {\n        sx: {\n          mr: 1\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        component: \"div\",\n        sx: {\n          flexGrow: 1\n        },\n        children: \"Salon Management System\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), !isMobile ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 0.5\n        },\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: () => handleNavigation(item.path),\n          sx: {\n            backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.15)' : 'transparent',\n            '&:hover': {\n              backgroundColor: 'rgba(255,255,255,0.2)'\n            },\n            textTransform: 'none',\n            fontSize: '0.875rem',\n            px: 2\n          },\n          children: item.text\n        }, item.text, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 0.5,\n          flexWrap: 'wrap'\n        },\n        children: menuItems.slice(0, 3).map(item => /*#__PURE__*/_jsxDEV(Button, {\n          color: \"inherit\",\n          size: \"small\",\n          onClick: () => handleNavigation(item.path),\n          sx: {\n            backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.15)' : 'transparent',\n            '&:hover': {\n              backgroundColor: 'rgba(255,255,255,0.2)'\n            },\n            textTransform: 'none',\n            fontSize: '0.75rem',\n            px: 1,\n            minWidth: 'auto'\n          },\n          children: item.text\n        }, item.text, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"U1Fp97TqvWKdCm8vEKOq+ut6eTU=\", false, function () {\n  return [useNavigate, useLocation, useTheme, useMediaQuery];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "useTheme", "useMediaQuery", "ContentCut", "SalonIcon", "useNavigate", "useLocation", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "navigate", "location", "theme", "isMobile", "breakpoints", "down", "menuItems", "text", "path", "handleNavigation", "position", "sx", "zIndex", "children", "minHeight", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "flexGrow", "display", "gap", "map", "item", "color", "size", "onClick", "backgroundColor", "pathname", "textTransform", "fontSize", "px", "flexWrap", "slice", "min<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport {\n  AppBar,\n  Toolbar,\n  Typography,\n  Button,\n  Box,\n  useTheme,\n  useMediaQuery,\n} from '@mui/material';\nimport {\n  ContentCut as SalonIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\n\nconst Navbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n\n  const menuItems = [\n    { text: 'Dashboard', path: '/' },\n    { text: 'Appointments', path: '/appointments' },\n    { text: 'Customers', path: '/customers' },\n    { text: 'Services', path: '/services' },\n    { text: 'Staff', path: '/staff' },\n    { text: 'Reports', path: '/reports' },\n  ];\n\n  const handleNavigation = (path) => {\n    navigate(path);\n  };\n\n  return (\n    <AppBar position=\"fixed\" sx={{ zIndex: 1000 }}>\n      <Toolbar sx={{ minHeight: '56px !important' }}>\n        <SalonIcon sx={{ mr: 1 }} />\n        <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1 }}>\n          Salon Management System\n        </Typography>\n\n        {!isMobile ? (\n          <Box sx={{ display: 'flex', gap: 0.5 }}>\n            {menuItems.map((item) => (\n              <Button\n                key={item.text}\n                color=\"inherit\"\n                size=\"small\"\n                onClick={() => handleNavigation(item.path)}\n                sx={{\n                  backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.15)' : 'transparent',\n                  '&:hover': {\n                    backgroundColor: 'rgba(255,255,255,0.2)',\n                  },\n                  textTransform: 'none',\n                  fontSize: '0.875rem',\n                  px: 2,\n                }}\n              >\n                {item.text}\n              </Button>\n            ))}\n          </Box>\n        ) : (\n          <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>\n            {menuItems.slice(0, 3).map((item) => (\n              <Button\n                key={item.text}\n                color=\"inherit\"\n                size=\"small\"\n                onClick={() => handleNavigation(item.path)}\n                sx={{\n                  backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.15)' : 'transparent',\n                  '&:hover': {\n                    backgroundColor: 'rgba(255,255,255,0.2)',\n                  },\n                  textTransform: 'none',\n                  fontSize: '0.75rem',\n                  px: 1,\n                  minWidth: 'auto',\n                }}\n              >\n                {item.text}\n              </Button>\n            ))}\n          </Box>\n        )}\n      </Toolbar>\n    </AppBar>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,SACEC,UAAU,IAAIC,SAAS,QAClB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAMO,KAAK,GAAGZ,QAAQ,CAAC,CAAC;EACxB,MAAMa,QAAQ,GAAGZ,aAAa,CAACW,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EAChC;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAgB,CAAC,EAC/C;IAAED,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa,CAAC,EACzC;IAAED,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACvC;IAAED,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAS,CAAC,EACjC;IAAED,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,CACtC;EAED,MAAMC,gBAAgB,GAAID,IAAI,IAAK;IACjCR,QAAQ,CAACQ,IAAI,CAAC;EAChB,CAAC;EAED,oBACEX,OAAA,CAACZ,MAAM;IAACyB,QAAQ,EAAC,OAAO;IAACC,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAK,CAAE;IAAAC,QAAA,eAC5ChB,OAAA,CAACX,OAAO;MAACyB,EAAE,EAAE;QAAEG,SAAS,EAAE;MAAkB,CAAE;MAAAD,QAAA,gBAC5ChB,OAAA,CAACJ,SAAS;QAACkB,EAAE,EAAE;UAAEI,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5BtB,OAAA,CAACV,UAAU;QAACiC,OAAO,EAAC,IAAI;QAACC,SAAS,EAAC,KAAK;QAACV,EAAE,EAAE;UAAEW,QAAQ,EAAE;QAAE,CAAE;QAAAT,QAAA,EAAC;MAE9D;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZ,CAAChB,QAAQ,gBACRN,OAAA,CAACR,GAAG;QAACsB,EAAE,EAAE;UAAEY,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAI,CAAE;QAAAX,QAAA,EACpCP,SAAS,CAACmB,GAAG,CAAEC,IAAI,iBAClB7B,OAAA,CAACT,MAAM;UAELuC,KAAK,EAAC,SAAS;UACfC,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACiB,IAAI,CAAClB,IAAI,CAAE;UAC3CG,EAAE,EAAE;YACFmB,eAAe,EAAE7B,QAAQ,CAAC8B,QAAQ,KAAKL,IAAI,CAAClB,IAAI,GAAG,wBAAwB,GAAG,aAAa;YAC3F,SAAS,EAAE;cACTsB,eAAe,EAAE;YACnB,CAAC;YACDE,aAAa,EAAE,MAAM;YACrBC,QAAQ,EAAE,UAAU;YACpBC,EAAE,EAAE;UACN,CAAE;UAAArB,QAAA,EAEDa,IAAI,CAACnB;QAAI,GAdLmB,IAAI,CAACnB,IAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeR,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAENtB,OAAA,CAACR,GAAG;QAACsB,EAAE,EAAE;UAAEY,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,GAAG;UAAEW,QAAQ,EAAE;QAAO,CAAE;QAAAtB,QAAA,EACtDP,SAAS,CAAC8B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACX,GAAG,CAAEC,IAAI,iBAC9B7B,OAAA,CAACT,MAAM;UAELuC,KAAK,EAAC,SAAS;UACfC,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACiB,IAAI,CAAClB,IAAI,CAAE;UAC3CG,EAAE,EAAE;YACFmB,eAAe,EAAE7B,QAAQ,CAAC8B,QAAQ,KAAKL,IAAI,CAAClB,IAAI,GAAG,wBAAwB,GAAG,aAAa;YAC3F,SAAS,EAAE;cACTsB,eAAe,EAAE;YACnB,CAAC;YACDE,aAAa,EAAE,MAAM;YACrBC,QAAQ,EAAE,SAAS;YACnBC,EAAE,EAAE,CAAC;YACLG,QAAQ,EAAE;UACZ,CAAE;UAAAxB,QAAA,EAEDa,IAAI,CAACnB;QAAI,GAfLmB,IAAI,CAACnB,IAAI;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBR,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEb,CAAC;AAACpB,EAAA,CA5EID,MAAM;EAAA,QACOJ,WAAW,EACXC,WAAW,EACdL,QAAQ,EACLC,aAAa;AAAA;AAAA+C,EAAA,GAJ1BxC,MAAM;AA8EZ,eAAeA,MAAM;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}