{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\UserProfile.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Paper, Typography, Box, Avatar, Grid, TextField, Button, Chip, Divider, Alert, Card, CardContent } from '@mui/material';\nimport { Person, Email, Phone, Badge, Security, Edit, Save, Cancel } from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserProfile = () => {\n  _s();\n  var _user$name;\n  const {\n    user\n  } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [editData, setEditData] = useState({\n    name: (user === null || user === void 0 ? void 0 : user.name) || '',\n    email: (user === null || user === void 0 ? void 0 : user.email) || '',\n    phone: (user === null || user === void 0 ? void 0 : user.phone) || ''\n  });\n  const [message, setMessage] = useState('');\n  const handleEditToggle = () => {\n    if (isEditing) {\n      // Reset data when canceling\n      setEditData({\n        name: (user === null || user === void 0 ? void 0 : user.name) || '',\n        email: (user === null || user === void 0 ? void 0 : user.email) || '',\n        phone: (user === null || user === void 0 ? void 0 : user.phone) || ''\n      });\n    }\n    setIsEditing(!isEditing);\n    setMessage('');\n  };\n  const handleInputChange = e => {\n    setEditData({\n      ...editData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSave = () => {\n    // In a real app, this would make an API call to update user data\n    setMessage('Profile updated successfully!');\n    setIsEditing(false);\n\n    // Simulate updating the user data\n    // In real implementation, you'd update the context or make an API call\n    setTimeout(() => setMessage(''), 3000);\n  };\n  const getRoleColor = role => {\n    switch (role) {\n      case 'admin':\n        return 'error';\n      case 'staff':\n        return 'primary';\n      case 'customer':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n  const getRoleIcon = role => {\n    switch (role) {\n      case 'admin':\n        return /*#__PURE__*/_jsxDEV(Security, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 16\n        }, this);\n      case 'staff':\n        return /*#__PURE__*/_jsxDEV(Badge, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 16\n        }, this);\n      case 'customer':\n        return /*#__PURE__*/_jsxDEV(Person, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Person, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"md\",\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        children: \"User not found. Please log in again.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mt: 4,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        gutterBottom: true,\n        children: \"User Profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"success\",\n        sx: {\n          mb: 3\n        },\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                mb: 3\n              },\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                sx: {\n                  width: 80,\n                  height: 80,\n                  bgcolor: 'primary.main',\n                  fontSize: '2rem',\n                  mr: 3\n                },\n                children: (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.charAt(0).toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flexGrow: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h5\",\n                  gutterBottom: true,\n                  children: user.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1,\n                    alignItems: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Chip, {\n                    icon: getRoleIcon(user.role),\n                    label: user.role.charAt(0).toUpperCase() + user.role.slice(1),\n                    color: getRoleColor(user.role),\n                    variant: \"outlined\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this), user.position && /*#__PURE__*/_jsxDEV(Chip, {\n                    label: user.position,\n                    variant: \"outlined\",\n                    color: \"default\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 146,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: isEditing ? \"outlined\" : \"contained\",\n                startIcon: isEditing ? /*#__PURE__*/_jsxDEV(Cancel, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 42\n                }, this) : /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 55\n                }, this),\n                onClick: handleEditToggle,\n                color: isEditing ? \"secondary\" : \"primary\",\n                children: isEditing ? 'Cancel' : 'Edit Profile'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Personal Information\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                mb: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 3,\n              children: [/*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Full Name\",\n                  name: \"name\",\n                  value: isEditing ? editData.name : user.name,\n                  onChange: handleInputChange,\n                  disabled: !isEditing,\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(Person, {\n                      sx: {\n                        mr: 1,\n                        color: 'action.active'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 184,\n                      columnNumber: 39\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Email Address\",\n                  name: \"email\",\n                  type: \"email\",\n                  value: isEditing ? editData.email : user.email,\n                  onChange: handleInputChange,\n                  disabled: !isEditing,\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(Email, {\n                      sx: {\n                        mr: 1,\n                        color: 'action.active'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 39\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 17\n              }, this), user.phone && /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Phone Number\",\n                  name: \"phone\",\n                  value: isEditing ? editData.phone : user.phone,\n                  onChange: handleInputChange,\n                  disabled: !isEditing,\n                  InputProps: {\n                    startAdornment: /*#__PURE__*/_jsxDEV(Phone, {\n                      sx: {\n                        mr: 1,\n                        color: 'action.active'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 41\n                    }, this)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"Username\",\n                  value: user.username,\n                  disabled: true,\n                  helperText: \"Username cannot be changed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), isEditing && /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2,\n                    justifyContent: 'flex-end'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"outlined\",\n                    onClick: handleEditToggle,\n                    startIcon: /*#__PURE__*/_jsxDEV(Cancel, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 36\n                    }, this),\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    variant: \"contained\",\n                    onClick: handleSave,\n                    startIcon: /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 36\n                    }, this),\n                    children: \"Save Changes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 4,\n          children: /*#__PURE__*/_jsxDEV(Paper, {\n            sx: {\n              p: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"Access & Permissions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Divider, {\n              sx: {\n                mb: 3\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              sx: {\n                mb: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Role\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  icon: getRoleIcon(user.role),\n                  label: user.role.charAt(0).toUpperCase() + user.role.slice(1),\n                  color: getRoleColor(user.role)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), user.permissions && /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle2\",\n                  gutterBottom: true,\n                  children: \"Permissions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 1\n                  },\n                  children: user.permissions.map((permission, index) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: permission,\n                    size: \"small\",\n                    variant: \"outlined\"\n                  }, index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 17\n            }, this), user.role === 'admin' && /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"info\",\n              sx: {\n                mt: 2\n              },\n              children: \"As an administrator, you have full access to all features.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(UserProfile, \"tFUe9FknWfpKS9rNq6s52OxE5gA=\", false, function () {\n  return [useAuth];\n});\n_c = UserProfile;\nexport default UserProfile;\nvar _c;\n$RefreshReg$(_c, \"UserProfile\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Paper", "Typography", "Box", "Avatar", "Grid", "TextField", "<PERSON><PERSON>", "Chip", "Divider", "<PERSON><PERSON>", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Person", "Email", "Phone", "Badge", "Security", "Edit", "Save", "Cancel", "useAuth", "jsxDEV", "_jsxDEV", "UserProfile", "_s", "_user$name", "user", "isEditing", "setIsEditing", "editData", "setEditData", "name", "email", "phone", "message", "setMessage", "handleEditToggle", "handleInputChange", "e", "target", "value", "handleSave", "setTimeout", "getRoleColor", "role", "getRoleIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "children", "severity", "sx", "mt", "mb", "variant", "gutterBottom", "container", "spacing", "item", "xs", "p", "display", "alignItems", "width", "height", "bgcolor", "fontSize", "mr", "char<PERSON>t", "toUpperCase", "flexGrow", "gap", "icon", "label", "slice", "color", "position", "startIcon", "onClick", "md", "fullWidth", "onChange", "disabled", "InputProps", "startAdornment", "type", "username", "helperText", "justifyContent", "permissions", "flexWrap", "map", "permission", "index", "size", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/UserProfile.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Container,\n  Paper,\n  Typography,\n  Box,\n  Avatar,\n  Grid,\n  TextField,\n  Button,\n  Chip,\n  Divider,\n  Alert,\n  Card,\n  CardContent\n} from '@mui/material';\nimport {\n  Person,\n  Email,\n  Phone,\n  Badge,\n  Security,\n  Edit,\n  Save,\n  Cancel\n} from '@mui/icons-material';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst UserProfile = () => {\n  const { user } = useAuth();\n  const [isEditing, setIsEditing] = useState(false);\n  const [editData, setEditData] = useState({\n    name: user?.name || '',\n    email: user?.email || '',\n    phone: user?.phone || ''\n  });\n  const [message, setMessage] = useState('');\n\n  const handleEditToggle = () => {\n    if (isEditing) {\n      // Reset data when canceling\n      setEditData({\n        name: user?.name || '',\n        email: user?.email || '',\n        phone: user?.phone || ''\n      });\n    }\n    setIsEditing(!isEditing);\n    setMessage('');\n  };\n\n  const handleInputChange = (e) => {\n    setEditData({\n      ...editData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSave = () => {\n    // In a real app, this would make an API call to update user data\n    setMessage('Profile updated successfully!');\n    setIsEditing(false);\n    \n    // Simulate updating the user data\n    // In real implementation, you'd update the context or make an API call\n    setTimeout(() => setMessage(''), 3000);\n  };\n\n  const getRoleColor = (role) => {\n    switch (role) {\n      case 'admin':\n        return 'error';\n      case 'staff':\n        return 'primary';\n      case 'customer':\n        return 'success';\n      default:\n        return 'default';\n    }\n  };\n\n  const getRoleIcon = (role) => {\n    switch (role) {\n      case 'admin':\n        return <Security />;\n      case 'staff':\n        return <Badge />;\n      case 'customer':\n        return <Person />;\n      default:\n        return <Person />;\n    }\n  };\n\n  if (!user) {\n    return (\n      <Container maxWidth=\"md\">\n        <Alert severity=\"error\">\n          User not found. Please log in again.\n        </Alert>\n      </Container>\n    );\n  }\n\n  return (\n    <Container maxWidth=\"md\">\n      <Box sx={{ mt: 4, mb: 4 }}>\n        <Typography variant=\"h4\" gutterBottom>\n          User Profile\n        </Typography>\n\n        {message && (\n          <Alert severity=\"success\" sx={{ mb: 3 }}>\n            {message}\n          </Alert>\n        )}\n\n        <Grid container spacing={3}>\n          {/* Profile Header */}\n          <Grid item xs={12}>\n            <Paper sx={{ p: 3 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                <Avatar\n                  sx={{\n                    width: 80,\n                    height: 80,\n                    bgcolor: 'primary.main',\n                    fontSize: '2rem',\n                    mr: 3\n                  }}\n                >\n                  {user.name?.charAt(0).toUpperCase()}\n                </Avatar>\n                <Box sx={{ flexGrow: 1 }}>\n                  <Typography variant=\"h5\" gutterBottom>\n                    {user.name}\n                  </Typography>\n                  <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>\n                    <Chip\n                      icon={getRoleIcon(user.role)}\n                      label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}\n                      color={getRoleColor(user.role)}\n                      variant=\"outlined\"\n                    />\n                    {user.position && (\n                      <Chip\n                        label={user.position}\n                        variant=\"outlined\"\n                        color=\"default\"\n                      />\n                    )}\n                  </Box>\n                </Box>\n                <Button\n                  variant={isEditing ? \"outlined\" : \"contained\"}\n                  startIcon={isEditing ? <Cancel /> : <Edit />}\n                  onClick={handleEditToggle}\n                  color={isEditing ? \"secondary\" : \"primary\"}\n                >\n                  {isEditing ? 'Cancel' : 'Edit Profile'}\n                </Button>\n              </Box>\n            </Paper>\n          </Grid>\n\n          {/* Profile Details */}\n          <Grid item xs={12} md={8}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Personal Information\n              </Typography>\n              <Divider sx={{ mb: 3 }} />\n\n              <Grid container spacing={3}>\n                <Grid item xs={12}>\n                  <TextField\n                    fullWidth\n                    label=\"Full Name\"\n                    name=\"name\"\n                    value={isEditing ? editData.name : user.name}\n                    onChange={handleInputChange}\n                    disabled={!isEditing}\n                    InputProps={{\n                      startAdornment: <Person sx={{ mr: 1, color: 'action.active' }} />\n                    }}\n                  />\n                </Grid>\n\n                <Grid item xs={12}>\n                  <TextField\n                    fullWidth\n                    label=\"Email Address\"\n                    name=\"email\"\n                    type=\"email\"\n                    value={isEditing ? editData.email : user.email}\n                    onChange={handleInputChange}\n                    disabled={!isEditing}\n                    InputProps={{\n                      startAdornment: <Email sx={{ mr: 1, color: 'action.active' }} />\n                    }}\n                  />\n                </Grid>\n\n                {user.phone && (\n                  <Grid item xs={12}>\n                    <TextField\n                      fullWidth\n                      label=\"Phone Number\"\n                      name=\"phone\"\n                      value={isEditing ? editData.phone : user.phone}\n                      onChange={handleInputChange}\n                      disabled={!isEditing}\n                      InputProps={{\n                        startAdornment: <Phone sx={{ mr: 1, color: 'action.active' }} />\n                      }}\n                    />\n                  </Grid>\n                )}\n\n                <Grid item xs={12}>\n                  <TextField\n                    fullWidth\n                    label=\"Username\"\n                    value={user.username}\n                    disabled\n                    helperText=\"Username cannot be changed\"\n                  />\n                </Grid>\n\n                {isEditing && (\n                  <Grid item xs={12}>\n                    <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>\n                      <Button\n                        variant=\"outlined\"\n                        onClick={handleEditToggle}\n                        startIcon={<Cancel />}\n                      >\n                        Cancel\n                      </Button>\n                      <Button\n                        variant=\"contained\"\n                        onClick={handleSave}\n                        startIcon={<Save />}\n                      >\n                        Save Changes\n                      </Button>\n                    </Box>\n                  </Grid>\n                )}\n              </Grid>\n            </Paper>\n          </Grid>\n\n          {/* Permissions & Role Info */}\n          <Grid item xs={12} md={4}>\n            <Paper sx={{ p: 3 }}>\n              <Typography variant=\"h6\" gutterBottom>\n                Access & Permissions\n              </Typography>\n              <Divider sx={{ mb: 3 }} />\n\n              <Card variant=\"outlined\" sx={{ mb: 2 }}>\n                <CardContent>\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Role\n                  </Typography>\n                  <Chip\n                    icon={getRoleIcon(user.role)}\n                    label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}\n                    color={getRoleColor(user.role)}\n                  />\n                </CardContent>\n              </Card>\n\n              {user.permissions && (\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Typography variant=\"subtitle2\" gutterBottom>\n                      Permissions\n                    </Typography>\n                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                      {user.permissions.map((permission, index) => (\n                        <Chip\n                          key={index}\n                          label={permission}\n                          size=\"small\"\n                          variant=\"outlined\"\n                        />\n                      ))}\n                    </Box>\n                  </CardContent>\n                </Card>\n              )}\n\n              {user.role === 'admin' && (\n                <Alert severity=\"info\" sx={{ mt: 2 }}>\n                  As an administrator, you have full access to all features.\n                </Alert>\n              )}\n            </Paper>\n          </Grid>\n        </Grid>\n      </Box>\n    </Container>\n  );\n};\n\nexport default UserProfile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,SAAS,EACTC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,WAAW,QACN,eAAe;AACtB,SACEC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,IAAI,EACJC,MAAM,QACD,qBAAqB;AAC5B,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA;EACxB,MAAM;IAAEC;EAAK,CAAC,GAAGN,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACO,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC;IACvCiC,IAAI,EAAE,CAAAL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,KAAI,EAAE;IACtBC,KAAK,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,KAAK,KAAI,EAAE;IACxBC,KAAK,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI;EACxB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMsC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAIT,SAAS,EAAE;MACb;MACAG,WAAW,CAAC;QACVC,IAAI,EAAE,CAAAL,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,IAAI,KAAI,EAAE;QACtBC,KAAK,EAAE,CAAAN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM,KAAK,KAAI,EAAE;QACxBC,KAAK,EAAE,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO,KAAK,KAAI;MACxB,CAAC,CAAC;IACJ;IACAL,YAAY,CAAC,CAACD,SAAS,CAAC;IACxBQ,UAAU,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,MAAME,iBAAiB,GAAIC,CAAC,IAAK;IAC/BR,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACS,CAAC,CAACC,MAAM,CAACR,IAAI,GAAGO,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACvB;IACAN,UAAU,CAAC,+BAA+B,CAAC;IAC3CP,YAAY,CAAC,KAAK,CAAC;;IAEnB;IACA;IACAc,UAAU,CAAC,MAAMP,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;EACxC,CAAC;EAED,MAAMQ,YAAY,GAAIC,IAAI,IAAK;IAC7B,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,OAAO;MAChB,KAAK,OAAO;QACV,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,WAAW,GAAID,IAAI,IAAK;IAC5B,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,oBAAOtB,OAAA,CAACN,QAAQ;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrB,KAAK,OAAO;QACV,oBAAO3B,OAAA,CAACP,KAAK;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClB,KAAK,UAAU;QACb,oBAAO3B,OAAA,CAACV,MAAM;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnB;QACE,oBAAO3B,OAAA,CAACV,MAAM;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACrB;EACF,CAAC;EAED,IAAI,CAACvB,IAAI,EAAE;IACT,oBACEJ,OAAA,CAACvB,SAAS;MAACmD,QAAQ,EAAC,IAAI;MAAAC,QAAA,eACtB7B,OAAA,CAACb,KAAK;QAAC2C,QAAQ,EAAC,OAAO;QAAAD,QAAA,EAAC;MAExB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACE3B,OAAA,CAACvB,SAAS;IAACmD,QAAQ,EAAC,IAAI;IAAAC,QAAA,eACtB7B,OAAA,CAACpB,GAAG;MAACmD,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACxB7B,OAAA,CAACrB,UAAU;QAACuD,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAN,QAAA,EAAC;MAEtC;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EAEZf,OAAO,iBACNZ,OAAA,CAACb,KAAK;QAAC2C,QAAQ,EAAC,SAAS;QAACC,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAJ,QAAA,EACrCjB;MAAO;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,eAED3B,OAAA,CAAClB,IAAI;QAACsD,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAR,QAAA,gBAEzB7B,OAAA,CAAClB,IAAI;UAACwD,IAAI;UAACC,EAAE,EAAE,EAAG;UAAAV,QAAA,eAChB7B,OAAA,CAACtB,KAAK;YAACqD,EAAE,EAAE;cAAES,CAAC,EAAE;YAAE,CAAE;YAAAX,QAAA,eAClB7B,OAAA,CAACpB,GAAG;cAACmD,EAAE,EAAE;gBAAEU,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAET,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,gBACxD7B,OAAA,CAACnB,MAAM;gBACLkD,EAAE,EAAE;kBACFY,KAAK,EAAE,EAAE;kBACTC,MAAM,EAAE,EAAE;kBACVC,OAAO,EAAE,cAAc;kBACvBC,QAAQ,EAAE,MAAM;kBAChBC,EAAE,EAAE;gBACN,CAAE;gBAAAlB,QAAA,GAAA1B,UAAA,GAEDC,IAAI,CAACK,IAAI,cAAAN,UAAA,uBAATA,UAAA,CAAW6C,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACT3B,OAAA,CAACpB,GAAG;gBAACmD,EAAE,EAAE;kBAAEmB,QAAQ,EAAE;gBAAE,CAAE;gBAAArB,QAAA,gBACvB7B,OAAA,CAACrB,UAAU;kBAACuD,OAAO,EAAC,IAAI;kBAACC,YAAY;kBAAAN,QAAA,EAClCzB,IAAI,CAACK;gBAAI;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACb3B,OAAA,CAACpB,GAAG;kBAACmD,EAAE,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEU,GAAG,EAAE,CAAC;oBAAET,UAAU,EAAE;kBAAS,CAAE;kBAAAb,QAAA,gBACzD7B,OAAA,CAACf,IAAI;oBACHmE,IAAI,EAAE7B,WAAW,CAACnB,IAAI,CAACkB,IAAI,CAAE;oBAC7B+B,KAAK,EAAEjD,IAAI,CAACkB,IAAI,CAAC0B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG7C,IAAI,CAACkB,IAAI,CAACgC,KAAK,CAAC,CAAC,CAAE;oBAC9DC,KAAK,EAAElC,YAAY,CAACjB,IAAI,CAACkB,IAAI,CAAE;oBAC/BY,OAAO,EAAC;kBAAU;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB,CAAC,EACDvB,IAAI,CAACoD,QAAQ,iBACZxD,OAAA,CAACf,IAAI;oBACHoE,KAAK,EAAEjD,IAAI,CAACoD,QAAS;oBACrBtB,OAAO,EAAC,UAAU;oBAClBqB,KAAK,EAAC;kBAAS;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CACF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3B,OAAA,CAAChB,MAAM;gBACLkD,OAAO,EAAE7B,SAAS,GAAG,UAAU,GAAG,WAAY;gBAC9CoD,SAAS,EAAEpD,SAAS,gBAAGL,OAAA,CAACH,MAAM;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG3B,OAAA,CAACL,IAAI;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC7C+B,OAAO,EAAE5C,gBAAiB;gBAC1ByC,KAAK,EAAElD,SAAS,GAAG,WAAW,GAAG,SAAU;gBAAAwB,QAAA,EAE1CxB,SAAS,GAAG,QAAQ,GAAG;cAAc;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGP3B,OAAA,CAAClB,IAAI;UAACwD,IAAI;UAACC,EAAE,EAAE,EAAG;UAACoB,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvB7B,OAAA,CAACtB,KAAK;YAACqD,EAAE,EAAE;cAAES,CAAC,EAAE;YAAE,CAAE;YAAAX,QAAA,gBAClB7B,OAAA,CAACrB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAN,QAAA,EAAC;YAEtC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3B,OAAA,CAACd,OAAO;cAAC6C,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE1B3B,OAAA,CAAClB,IAAI;cAACsD,SAAS;cAACC,OAAO,EAAE,CAAE;cAAAR,QAAA,gBACzB7B,OAAA,CAAClB,IAAI;gBAACwD,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAV,QAAA,eAChB7B,OAAA,CAACjB,SAAS;kBACR6E,SAAS;kBACTP,KAAK,EAAC,WAAW;kBACjB5C,IAAI,EAAC,MAAM;kBACXS,KAAK,EAAEb,SAAS,GAAGE,QAAQ,CAACE,IAAI,GAAGL,IAAI,CAACK,IAAK;kBAC7CoD,QAAQ,EAAE9C,iBAAkB;kBAC5B+C,QAAQ,EAAE,CAACzD,SAAU;kBACrB0D,UAAU,EAAE;oBACVC,cAAc,eAAEhE,OAAA,CAACV,MAAM;sBAACyC,EAAE,EAAE;wBAAEgB,EAAE,EAAE,CAAC;wBAAEQ,KAAK,EAAE;sBAAgB;oBAAE;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAClE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAEP3B,OAAA,CAAClB,IAAI;gBAACwD,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAV,QAAA,eAChB7B,OAAA,CAACjB,SAAS;kBACR6E,SAAS;kBACTP,KAAK,EAAC,eAAe;kBACrB5C,IAAI,EAAC,OAAO;kBACZwD,IAAI,EAAC,OAAO;kBACZ/C,KAAK,EAAEb,SAAS,GAAGE,QAAQ,CAACG,KAAK,GAAGN,IAAI,CAACM,KAAM;kBAC/CmD,QAAQ,EAAE9C,iBAAkB;kBAC5B+C,QAAQ,EAAE,CAACzD,SAAU;kBACrB0D,UAAU,EAAE;oBACVC,cAAc,eAAEhE,OAAA,CAACT,KAAK;sBAACwC,EAAE,EAAE;wBAAEgB,EAAE,EAAE,CAAC;wBAAEQ,KAAK,EAAE;sBAAgB;oBAAE;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACjE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAENvB,IAAI,CAACO,KAAK,iBACTX,OAAA,CAAClB,IAAI;gBAACwD,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAV,QAAA,eAChB7B,OAAA,CAACjB,SAAS;kBACR6E,SAAS;kBACTP,KAAK,EAAC,cAAc;kBACpB5C,IAAI,EAAC,OAAO;kBACZS,KAAK,EAAEb,SAAS,GAAGE,QAAQ,CAACI,KAAK,GAAGP,IAAI,CAACO,KAAM;kBAC/CkD,QAAQ,EAAE9C,iBAAkB;kBAC5B+C,QAAQ,EAAE,CAACzD,SAAU;kBACrB0D,UAAU,EAAE;oBACVC,cAAc,eAAEhE,OAAA,CAACR,KAAK;sBAACuC,EAAE,EAAE;wBAAEgB,EAAE,EAAE,CAAC;wBAAEQ,KAAK,EAAE;sBAAgB;oBAAE;sBAAA/B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBACjE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CACP,eAED3B,OAAA,CAAClB,IAAI;gBAACwD,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAV,QAAA,eAChB7B,OAAA,CAACjB,SAAS;kBACR6E,SAAS;kBACTP,KAAK,EAAC,UAAU;kBAChBnC,KAAK,EAAEd,IAAI,CAAC8D,QAAS;kBACrBJ,QAAQ;kBACRK,UAAU,EAAC;gBAA4B;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAENtB,SAAS,iBACRL,OAAA,CAAClB,IAAI;gBAACwD,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAAAV,QAAA,eAChB7B,OAAA,CAACpB,GAAG;kBAACmD,EAAE,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAEU,GAAG,EAAE,CAAC;oBAAEiB,cAAc,EAAE;kBAAW,CAAE;kBAAAvC,QAAA,gBAC/D7B,OAAA,CAAChB,MAAM;oBACLkD,OAAO,EAAC,UAAU;oBAClBwB,OAAO,EAAE5C,gBAAiB;oBAC1B2C,SAAS,eAAEzD,OAAA,CAACH,MAAM;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAE,QAAA,EACvB;kBAED;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT3B,OAAA,CAAChB,MAAM;oBACLkD,OAAO,EAAC,WAAW;oBACnBwB,OAAO,EAAEvC,UAAW;oBACpBsC,SAAS,eAAEzD,OAAA,CAACJ,IAAI;sBAAA4B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAAAE,QAAA,EACrB;kBAED;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGP3B,OAAA,CAAClB,IAAI;UAACwD,IAAI;UAACC,EAAE,EAAE,EAAG;UAACoB,EAAE,EAAE,CAAE;UAAA9B,QAAA,eACvB7B,OAAA,CAACtB,KAAK;YAACqD,EAAE,EAAE;cAAES,CAAC,EAAE;YAAE,CAAE;YAAAX,QAAA,gBAClB7B,OAAA,CAACrB,UAAU;cAACuD,OAAO,EAAC,IAAI;cAACC,YAAY;cAAAN,QAAA,EAAC;YAEtC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb3B,OAAA,CAACd,OAAO;cAAC6C,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE;YAAE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAE1B3B,OAAA,CAACZ,IAAI;cAAC8C,OAAO,EAAC,UAAU;cAACH,EAAE,EAAE;gBAAEE,EAAE,EAAE;cAAE,CAAE;cAAAJ,QAAA,eACrC7B,OAAA,CAACX,WAAW;gBAAAwC,QAAA,gBACV7B,OAAA,CAACrB,UAAU;kBAACuD,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAAAN,QAAA,EAAC;gBAE7C;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3B,OAAA,CAACf,IAAI;kBACHmE,IAAI,EAAE7B,WAAW,CAACnB,IAAI,CAACkB,IAAI,CAAE;kBAC7B+B,KAAK,EAAEjD,IAAI,CAACkB,IAAI,CAAC0B,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG7C,IAAI,CAACkB,IAAI,CAACgC,KAAK,CAAC,CAAC,CAAE;kBAC9DC,KAAK,EAAElC,YAAY,CAACjB,IAAI,CAACkB,IAAI;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,EAENvB,IAAI,CAACiE,WAAW,iBACfrE,OAAA,CAACZ,IAAI;cAAC8C,OAAO,EAAC,UAAU;cAAAL,QAAA,eACtB7B,OAAA,CAACX,WAAW;gBAAAwC,QAAA,gBACV7B,OAAA,CAACrB,UAAU;kBAACuD,OAAO,EAAC,WAAW;kBAACC,YAAY;kBAAAN,QAAA,EAAC;gBAE7C;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACb3B,OAAA,CAACpB,GAAG;kBAACmD,EAAE,EAAE;oBAAEU,OAAO,EAAE,MAAM;oBAAE6B,QAAQ,EAAE,MAAM;oBAAEnB,GAAG,EAAE;kBAAE,CAAE;kBAAAtB,QAAA,EACpDzB,IAAI,CAACiE,WAAW,CAACE,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK,kBACtCzE,OAAA,CAACf,IAAI;oBAEHoE,KAAK,EAAEmB,UAAW;oBAClBE,IAAI,EAAC,OAAO;oBACZxC,OAAO,EAAC;kBAAU,GAHbuC,KAAK;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIX,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACP,EAEAvB,IAAI,CAACkB,IAAI,KAAK,OAAO,iBACpBtB,OAAA,CAACb,KAAK;cAAC2C,QAAQ,EAAC,MAAM;cAACC,EAAE,EAAE;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAH,QAAA,EAAC;YAEtC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB,CAAC;AAACzB,EAAA,CArRID,WAAW;EAAA,QACEH,OAAO;AAAA;AAAA6E,EAAA,GADpB1E,WAAW;AAuRjB,eAAeA,WAAW;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}