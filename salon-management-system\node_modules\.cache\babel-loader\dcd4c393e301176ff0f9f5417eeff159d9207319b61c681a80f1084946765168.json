{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiStepLabel', slot);\n}\nconst stepLabelClasses = generateUtilityClasses('MuiStepLabel', ['root', 'horizontal', 'vertical', 'label', 'active', 'completed', 'error', 'disabled', 'iconContainer', 'alternativeLabel', 'labelContainer']);\nexport default stepLabelClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getStepLabelUtilityClass", "slot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/StepLabel/stepLabelClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getStepLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiStepLabel', slot);\n}\nconst stepLabelClasses = generateUtilityClasses('MuiStepLabel', ['root', 'horizontal', 'vertical', 'label', 'active', 'completed', 'error', 'disabled', 'iconContainer', 'alternativeLabel', 'labelContainer']);\nexport default stepLabelClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;AAC/M,eAAeI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}