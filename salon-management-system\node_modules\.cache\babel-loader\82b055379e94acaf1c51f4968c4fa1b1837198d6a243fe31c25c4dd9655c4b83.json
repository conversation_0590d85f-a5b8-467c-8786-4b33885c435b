{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableBodyUtilityClass(slot) {\n  return generateUtilityClass('MuiTableBody', slot);\n}\nconst tableBodyClasses = generateUtilityClasses('MuiTableBody', ['root']);\nexport default tableBodyClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTableBodyUtilityClass", "slot", "tableBodyClasses"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/TableBody/tableBodyClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableBodyUtilityClass(slot) {\n  return generateUtilityClass('MuiTableBody', slot);\n}\nconst tableBodyClasses = generateUtilityClasses('MuiTableBody', ['root']);\nexport default tableBodyClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOF,oBAAoB,CAAC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGJ,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC;AACzE,eAAeI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}