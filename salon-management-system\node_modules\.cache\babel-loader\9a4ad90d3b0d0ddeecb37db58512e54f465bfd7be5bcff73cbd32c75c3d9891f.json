{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as MuiGlobalStyles, internal_serializeStyles as serializeStyles } from '@mui/styled-engine';\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction wrapGlobalLayer(styles) {\n  const serialized = serializeStyles(styles);\n  if (styles !== serialized && serialized.styles) {\n    if (!serialized.styles.match(/^@layer\\s+[^{]*$/)) {\n      // If the styles are not already wrapped in a layer, wrap them in a global layer.\n      serialized.styles = `@layer global{${serialized.styles}}`;\n    }\n    return serialized;\n  }\n  return styles;\n}\nfunction GlobalStyles({\n  styles,\n  themeId,\n  defaultTheme = {}\n}) {\n  const upperTheme = useTheme(defaultTheme);\n  const resolvedTheme = themeId ? upperTheme[themeId] || upperTheme : upperTheme;\n  let globalStyles = typeof styles === 'function' ? styles(resolvedTheme) : styles;\n  if (resolvedTheme.modularCssLayers) {\n    if (Array.isArray(globalStyles)) {\n      globalStyles = globalStyles.map(styleArg => {\n        if (typeof styleArg === 'function') {\n          return wrapGlobalLayer(styleArg(resolvedTheme));\n        }\n        return wrapGlobalLayer(styleArg);\n      });\n    } else {\n      globalStyles = wrapGlobalLayer(globalStyles);\n    }\n  }\n  return /*#__PURE__*/_jsx(MuiGlobalStyles, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  defaultTheme: PropTypes.object,\n  /**\n   * @ignore\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  themeId: PropTypes.string\n} : void 0;\nexport default GlobalStyles;", "map": {"version": 3, "names": ["React", "PropTypes", "GlobalStyles", "MuiGlobalStyles", "internal_serializeStyles", "serializeStyles", "useTheme", "jsx", "_jsx", "wrapGlobalLayer", "styles", "serialized", "match", "themeId", "defaultTheme", "upperTheme", "resolvedTheme", "globalStyles", "modularCssLayers", "Array", "isArray", "map", "styleArg", "process", "env", "NODE_ENV", "propTypes", "object", "oneOfType", "array", "func", "number", "string", "bool"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/system/esm/GlobalStyles/GlobalStyles.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { GlobalStyles as MuiGlobalStyles, internal_serializeStyles as serializeStyles } from '@mui/styled-engine';\nimport useTheme from \"../useTheme/index.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction wrapGlobalLayer(styles) {\n  const serialized = serializeStyles(styles);\n  if (styles !== serialized && serialized.styles) {\n    if (!serialized.styles.match(/^@layer\\s+[^{]*$/)) {\n      // If the styles are not already wrapped in a layer, wrap them in a global layer.\n      serialized.styles = `@layer global{${serialized.styles}}`;\n    }\n    return serialized;\n  }\n  return styles;\n}\nfunction GlobalStyles({\n  styles,\n  themeId,\n  defaultTheme = {}\n}) {\n  const upperTheme = useTheme(defaultTheme);\n  const resolvedTheme = themeId ? upperTheme[themeId] || upperTheme : upperTheme;\n  let globalStyles = typeof styles === 'function' ? styles(resolvedTheme) : styles;\n  if (resolvedTheme.modularCssLayers) {\n    if (Array.isArray(globalStyles)) {\n      globalStyles = globalStyles.map(styleArg => {\n        if (typeof styleArg === 'function') {\n          return wrapGlobalLayer(styleArg(resolvedTheme));\n        }\n        return wrapGlobalLayer(styleArg);\n      });\n    } else {\n      globalStyles = wrapGlobalLayer(globalStyles);\n    }\n  }\n  return /*#__PURE__*/_jsx(MuiGlobalStyles, {\n    styles: globalStyles\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? GlobalStyles.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  defaultTheme: PropTypes.object,\n  /**\n   * @ignore\n   */\n  styles: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.array, PropTypes.func, PropTypes.number, PropTypes.object, PropTypes.string, PropTypes.bool]),\n  /**\n   * @ignore\n   */\n  themeId: PropTypes.string\n} : void 0;\nexport default GlobalStyles;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,YAAY,IAAIC,eAAe,EAAEC,wBAAwB,IAAIC,eAAe,QAAQ,oBAAoB;AACjH,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,eAAeA,CAACC,MAAM,EAAE;EAC/B,MAAMC,UAAU,GAAGN,eAAe,CAACK,MAAM,CAAC;EAC1C,IAAIA,MAAM,KAAKC,UAAU,IAAIA,UAAU,CAACD,MAAM,EAAE;IAC9C,IAAI,CAACC,UAAU,CAACD,MAAM,CAACE,KAAK,CAAC,kBAAkB,CAAC,EAAE;MAChD;MACAD,UAAU,CAACD,MAAM,GAAG,iBAAiBC,UAAU,CAACD,MAAM,GAAG;IAC3D;IACA,OAAOC,UAAU;EACnB;EACA,OAAOD,MAAM;AACf;AACA,SAASR,YAAYA,CAAC;EACpBQ,MAAM;EACNG,OAAO;EACPC,YAAY,GAAG,CAAC;AAClB,CAAC,EAAE;EACD,MAAMC,UAAU,GAAGT,QAAQ,CAACQ,YAAY,CAAC;EACzC,MAAME,aAAa,GAAGH,OAAO,GAAGE,UAAU,CAACF,OAAO,CAAC,IAAIE,UAAU,GAAGA,UAAU;EAC9E,IAAIE,YAAY,GAAG,OAAOP,MAAM,KAAK,UAAU,GAAGA,MAAM,CAACM,aAAa,CAAC,GAAGN,MAAM;EAChF,IAAIM,aAAa,CAACE,gBAAgB,EAAE;IAClC,IAAIC,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;MAC/BA,YAAY,GAAGA,YAAY,CAACI,GAAG,CAACC,QAAQ,IAAI;QAC1C,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAClC,OAAOb,eAAe,CAACa,QAAQ,CAACN,aAAa,CAAC,CAAC;QACjD;QACA,OAAOP,eAAe,CAACa,QAAQ,CAAC;MAClC,CAAC,CAAC;IACJ,CAAC,MAAM;MACLL,YAAY,GAAGR,eAAe,CAACQ,YAAY,CAAC;IAC9C;EACF;EACA,OAAO,aAAaT,IAAI,CAACL,eAAe,EAAE;IACxCO,MAAM,EAAEO;EACV,CAAC,CAAC;AACJ;AACAM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,YAAY,CAACwB,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;EACEZ,YAAY,EAAEb,SAAS,CAAC0B,MAAM;EAC9B;AACF;AACA;EACEjB,MAAM,EAAET,SAAS,CAAC,sCAAsC2B,SAAS,CAAC,CAAC3B,SAAS,CAAC4B,KAAK,EAAE5B,SAAS,CAAC6B,IAAI,EAAE7B,SAAS,CAAC8B,MAAM,EAAE9B,SAAS,CAAC0B,MAAM,EAAE1B,SAAS,CAAC+B,MAAM,EAAE/B,SAAS,CAACgC,IAAI,CAAC,CAAC;EAC1K;AACF;AACA;EACEpB,OAAO,EAAEZ,SAAS,CAAC+B;AACrB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe9B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}