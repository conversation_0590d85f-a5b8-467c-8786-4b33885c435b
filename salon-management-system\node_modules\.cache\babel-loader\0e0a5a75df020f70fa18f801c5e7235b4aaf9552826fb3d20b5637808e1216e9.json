{"ast": null, "code": "import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nexport function paletteTransform(value, userValue) {\n  if (userValue === 'grey') {\n    return userValue;\n  }\n  return value;\n}\nexport const color = style({\n  prop: 'color',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nexport const bgcolor = style({\n  prop: 'bgcolor',\n  cssProperty: 'backgroundColor',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nexport const backgroundColor = style({\n  prop: 'backgroundColor',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nconst palette = compose(color, bgcolor, backgroundColor);\nexport default palette;", "map": {"version": 3, "names": ["style", "compose", "paletteTransform", "value", "userValue", "color", "prop", "<PERSON><PERSON><PERSON>", "transform", "bgcolor", "cssProperty", "backgroundColor", "palette"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/system/esm/palette/palette.js"], "sourcesContent": ["import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nexport function paletteTransform(value, userValue) {\n  if (userValue === 'grey') {\n    return userValue;\n  }\n  return value;\n}\nexport const color = style({\n  prop: 'color',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nexport const bgcolor = style({\n  prop: 'bgcolor',\n  cssProperty: 'backgroundColor',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nexport const backgroundColor = style({\n  prop: 'backgroundColor',\n  themeKey: 'palette',\n  transform: paletteTransform\n});\nconst palette = compose(color, bgcolor, backgroundColor);\nexport default palette;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACjD,IAAIA,SAAS,KAAK,MAAM,EAAE;IACxB,OAAOA,SAAS;EAClB;EACA,OAAOD,KAAK;AACd;AACA,OAAO,MAAME,KAAK,GAAGL,KAAK,CAAC;EACzBM,IAAI,EAAE,OAAO;EACbC,QAAQ,EAAE,SAAS;EACnBC,SAAS,EAAEN;AACb,CAAC,CAAC;AACF,OAAO,MAAMO,OAAO,GAAGT,KAAK,CAAC;EAC3BM,IAAI,EAAE,SAAS;EACfI,WAAW,EAAE,iBAAiB;EAC9BH,QAAQ,EAAE,SAAS;EACnBC,SAAS,EAAEN;AACb,CAAC,CAAC;AACF,OAAO,MAAMS,eAAe,GAAGX,KAAK,CAAC;EACnCM,IAAI,EAAE,iBAAiB;EACvBC,QAAQ,EAAE,SAAS;EACnBC,SAAS,EAAEN;AACb,CAAC,CAAC;AACF,MAAMU,OAAO,GAAGX,OAAO,CAACI,KAAK,EAAEI,OAAO,EAAEE,eAAe,CAAC;AACxD,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}