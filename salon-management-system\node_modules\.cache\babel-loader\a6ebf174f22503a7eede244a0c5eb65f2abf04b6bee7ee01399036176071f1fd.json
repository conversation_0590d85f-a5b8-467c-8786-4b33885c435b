{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\App.js\";\nimport React from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport Dashboard from './components/Dashboard';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#8e24aa'\n    },\n    secondary: {\n      main: '#ff4081'\n    },\n    background: {\n      default: '#f5f5f5'\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          style: {\n            marginTop: '64px',\n            padding: '20px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 30,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/appointments\",\n              element: /*#__PURE__*/_jsxDEV(Appointments, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 52\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/customers\",\n              element: /*#__PURE__*/_jsxDEV(Customers, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/services\",\n              element: /*#__PURE__*/_jsxDEV(Services, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/staff\",\n              element: /*#__PURE__*/_jsxDEV(Staff, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/reports\",\n              element: /*#__PURE__*/_jsxDEV(Reports, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "ThemeProvider", "createTheme", "CssBaseline", "Dashboard", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "secondary", "background", "default", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Router", "className", "<PERSON><PERSON><PERSON>", "style", "marginTop", "padding", "Routes", "Route", "path", "element", "Appointments", "Customers", "Services", "Staff", "Reports", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport Dashboard from './components/Dashboard';\nimport './App.css';\n\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#8e24aa',\n    },\n    secondary: {\n      main: '#ff4081',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Router>\n        <div className=\"App\">\n          <Navbar />\n          <main style={{ marginTop: '64px', padding: '20px' }}>\n            <Routes>\n              <Route path=\"/\" element={<Dashboard />} />\n              <Route path=\"/appointments\" element={<Appointments />} />\n              <Route path=\"/customers\" element={<Customers />} />\n              <Route path=\"/services\" element={<Services />} />\n              <Route path=\"/staff\" element={<Staff />} />\n              <Route path=\"/reports\" element={<Reports />} />\n            </Routes>\n          </main>\n        </div>\n      </Router>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,KAAK,GAAGL,WAAW,CAAC;EACxBM,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBACER,OAAA,CAACL,aAAa;IAACM,KAAK,EAAEA,KAAM;IAAAQ,QAAA,gBAC1BT,OAAA,CAACH,WAAW;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfb,OAAA,CAACc,MAAM;MAAAL,QAAA,eACLT,OAAA;QAAKe,SAAS,EAAC,KAAK;QAAAN,QAAA,gBAClBT,OAAA,CAACgB,MAAM;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVb,OAAA;UAAMiB,KAAK,EAAE;YAAEC,SAAS,EAAE,MAAM;YAAEC,OAAO,EAAE;UAAO,CAAE;UAAAV,QAAA,eAClDT,OAAA,CAACoB,MAAM;YAAAX,QAAA,gBACLT,OAAA,CAACqB,KAAK;cAACC,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEvB,OAAA,CAACF,SAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1Cb,OAAA,CAACqB,KAAK;cAACC,IAAI,EAAC,eAAe;cAACC,OAAO,eAAEvB,OAAA,CAACwB,YAAY;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzDb,OAAA,CAACqB,KAAK;cAACC,IAAI,EAAC,YAAY;cAACC,OAAO,eAAEvB,OAAA,CAACyB,SAAS;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDb,OAAA,CAACqB,KAAK;cAACC,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEvB,OAAA,CAAC0B,QAAQ;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDb,OAAA,CAACqB,KAAK;cAACC,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEvB,OAAA,CAAC2B,KAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3Cb,OAAA,CAACqB,KAAK;cAACC,IAAI,EAAC,UAAU;cAACC,OAAO,eAAEvB,OAAA,CAAC4B,OAAO;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACgB,EAAA,GArBQrB,GAAG;AAuBZ,eAAeA,GAAG;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}