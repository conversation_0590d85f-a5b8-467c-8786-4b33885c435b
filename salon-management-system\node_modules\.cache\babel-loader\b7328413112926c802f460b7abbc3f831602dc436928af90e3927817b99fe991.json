{"ast": null, "code": "import { from, trim, charat, strlen, substr, append, assign } from './Utility.js';\nexport var line = 1;\nexport var column = 1;\nexport var length = 0;\nexport var position = 0;\nexport var character = 0;\nexport var characters = '';\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {number} length\n */\nexport function node(value, root, parent, type, props, children, length) {\n  return {\n    value: value,\n    root: root,\n    parent: parent,\n    type: type,\n    props: props,\n    children: children,\n    line: line,\n    column: column,\n    length: length,\n    return: ''\n  };\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy(root, props) {\n  return assign(node('', null, null, '', null, null, 0), root, {\n    length: -root.length\n  }, props);\n}\n\n/**\n * @return {number}\n */\nexport function char() {\n  return character;\n}\n\n/**\n * @return {number}\n */\nexport function prev() {\n  character = position > 0 ? charat(characters, --position) : 0;\n  if (column--, character === 10) column = 1, line--;\n  return character;\n}\n\n/**\n * @return {number}\n */\nexport function next() {\n  character = position < length ? charat(characters, position++) : 0;\n  if (column++, character === 10) column = 1, line++;\n  return character;\n}\n\n/**\n * @return {number}\n */\nexport function peek() {\n  return charat(characters, position);\n}\n\n/**\n * @return {number}\n */\nexport function caret() {\n  return position;\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice(begin, end) {\n  return substr(characters, begin, end);\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token(type) {\n  switch (type) {\n    // \\0 \\t \\n \\r \\s whitespace token\n    case 0:\n    case 9:\n    case 10:\n    case 13:\n    case 32:\n      return 5;\n    // ! + , / > @ ~ isolate token\n    case 33:\n    case 43:\n    case 44:\n    case 47:\n    case 62:\n    case 64:\n    case 126:\n    // ; { } breakpoint token\n    case 59:\n    case 123:\n    case 125:\n      return 4;\n    // : accompanied token\n    case 58:\n      return 3;\n    // \" ' ( [ opening delimit token\n    case 34:\n    case 39:\n    case 40:\n    case 91:\n      return 2;\n    // ) ] closing delimit token\n    case 41:\n    case 93:\n      return 1;\n  }\n  return 0;\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc(value) {\n  return line = column = 1, length = strlen(characters = value), position = 0, [];\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc(value) {\n  return characters = '', value;\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit(type) {\n  return trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)));\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize(value) {\n  return dealloc(tokenizer(alloc(value)));\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace(type) {\n  while (character = peek()) if (character < 33) next();else break;\n  return token(type) > 2 || token(character) > 3 ? '' : ' ';\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer(children) {\n  while (next()) switch (token(character)) {\n    case 0:\n      append(identifier(position - 1), children);\n      break;\n    case 2:\n      append(delimit(character), children);\n      break;\n    default:\n      append(from(character), children);\n  }\n  return children;\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping(index, count) {\n  while (--count && next())\n  // not 0-9 A-F a-f\n  if (character < 48 || character > 102 || character > 57 && character < 65 || character > 70 && character < 97) break;\n  return slice(index, caret() + (count < 6 && peek() == 32 && next() == 32));\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter(type) {\n  while (next()) switch (character) {\n    // ] ) \" '\n    case type:\n      return position;\n    // \" '\n    case 34:\n    case 39:\n      if (type !== 34 && type !== 39) delimiter(character);\n      break;\n    // (\n    case 40:\n      if (type === 41) delimiter(type);\n      break;\n    // \\\n    case 92:\n      next();\n      break;\n  }\n  return position;\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter(type, index) {\n  while (next())\n  // //\n  if (type + character === 47 + 10) break;\n  // /*\n  else if (type + character === 42 + 42 && peek() === 47) break;\n  return '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next());\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier(index) {\n  while (!token(peek())) next();\n  return slice(index, position);\n}", "map": {"version": 3, "names": ["from", "trim", "charat", "strlen", "substr", "append", "assign", "line", "column", "length", "position", "character", "characters", "node", "value", "root", "parent", "type", "props", "children", "return", "copy", "char", "prev", "next", "peek", "caret", "slice", "begin", "end", "token", "alloc", "dealloc", "delimit", "delimiter", "tokenize", "tokenizer", "whitespace", "identifier", "escaping", "index", "count", "commenter"], "sources": ["D:/Project/salon-management-system/node_modules/stylis/src/Tokenizer.js"], "sourcesContent": ["import {from, trim, charat, strlen, substr, append, assign} from './Utility.js'\n\nexport var line = 1\nexport var column = 1\nexport var length = 0\nexport var position = 0\nexport var character = 0\nexport var characters = ''\n\n/**\n * @param {string} value\n * @param {object | null} root\n * @param {object | null} parent\n * @param {string} type\n * @param {string[] | string} props\n * @param {object[] | string} children\n * @param {number} length\n */\nexport function node (value, root, parent, type, props, children, length) {\n\treturn {value: value, root: root, parent: parent, type: type, props: props, children: children, line: line, column: column, length: length, return: ''}\n}\n\n/**\n * @param {object} root\n * @param {object} props\n * @return {object}\n */\nexport function copy (root, props) {\n\treturn assign(node('', null, null, '', null, null, 0), root, {length: -root.length}, props)\n}\n\n/**\n * @return {number}\n */\nexport function char () {\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function prev () {\n\tcharacter = position > 0 ? charat(characters, --position) : 0\n\n\tif (column--, character === 10)\n\t\tcolumn = 1, line--\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function next () {\n\tcharacter = position < length ? charat(characters, position++) : 0\n\n\tif (column++, character === 10)\n\t\tcolumn = 1, line++\n\n\treturn character\n}\n\n/**\n * @return {number}\n */\nexport function peek () {\n\treturn charat(characters, position)\n}\n\n/**\n * @return {number}\n */\nexport function caret () {\n\treturn position\n}\n\n/**\n * @param {number} begin\n * @param {number} end\n * @return {string}\n */\nexport function slice (begin, end) {\n\treturn substr(characters, begin, end)\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function token (type) {\n\tswitch (type) {\n\t\t// \\0 \\t \\n \\r \\s whitespace token\n\t\tcase 0: case 9: case 10: case 13: case 32:\n\t\t\treturn 5\n\t\t// ! + , / > @ ~ isolate token\n\t\tcase 33: case 43: case 44: case 47: case 62: case 64: case 126:\n\t\t// ; { } breakpoint token\n\t\tcase 59: case 123: case 125:\n\t\t\treturn 4\n\t\t// : accompanied token\n\t\tcase 58:\n\t\t\treturn 3\n\t\t// \" ' ( [ opening delimit token\n\t\tcase 34: case 39: case 40: case 91:\n\t\t\treturn 2\n\t\t// ) ] closing delimit token\n\t\tcase 41: case 93:\n\t\t\treturn 1\n\t}\n\n\treturn 0\n}\n\n/**\n * @param {string} value\n * @return {any[]}\n */\nexport function alloc (value) {\n\treturn line = column = 1, length = strlen(characters = value), position = 0, []\n}\n\n/**\n * @param {any} value\n * @return {any}\n */\nexport function dealloc (value) {\n\treturn characters = '', value\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function delimit (type) {\n\treturn trim(slice(position - 1, delimiter(type === 91 ? type + 2 : type === 40 ? type + 1 : type)))\n}\n\n/**\n * @param {string} value\n * @return {string[]}\n */\nexport function tokenize (value) {\n\treturn dealloc(tokenizer(alloc(value)))\n}\n\n/**\n * @param {number} type\n * @return {string}\n */\nexport function whitespace (type) {\n\twhile (character = peek())\n\t\tif (character < 33)\n\t\t\tnext()\n\t\telse\n\t\t\tbreak\n\n\treturn token(type) > 2 || token(character) > 3 ? '' : ' '\n}\n\n/**\n * @param {string[]} children\n * @return {string[]}\n */\nexport function tokenizer (children) {\n\twhile (next())\n\t\tswitch (token(character)) {\n\t\t\tcase 0: append(identifier(position - 1), children)\n\t\t\t\tbreak\n\t\t\tcase 2: append(delimit(character), children)\n\t\t\t\tbreak\n\t\t\tdefault: append(from(character), children)\n\t\t}\n\n\treturn children\n}\n\n/**\n * @param {number} index\n * @param {number} count\n * @return {string}\n */\nexport function escaping (index, count) {\n\twhile (--count && next())\n\t\t// not 0-9 A-F a-f\n\t\tif (character < 48 || character > 102 || (character > 57 && character < 65) || (character > 70 && character < 97))\n\t\t\tbreak\n\n\treturn slice(index, caret() + (count < 6 && peek() == 32 && next() == 32))\n}\n\n/**\n * @param {number} type\n * @return {number}\n */\nexport function delimiter (type) {\n\twhile (next())\n\t\tswitch (character) {\n\t\t\t// ] ) \" '\n\t\t\tcase type:\n\t\t\t\treturn position\n\t\t\t// \" '\n\t\t\tcase 34: case 39:\n\t\t\t\tif (type !== 34 && type !== 39)\n\t\t\t\t\tdelimiter(character)\n\t\t\t\tbreak\n\t\t\t// (\n\t\t\tcase 40:\n\t\t\t\tif (type === 41)\n\t\t\t\t\tdelimiter(type)\n\t\t\t\tbreak\n\t\t\t// \\\n\t\t\tcase 92:\n\t\t\t\tnext()\n\t\t\t\tbreak\n\t\t}\n\n\treturn position\n}\n\n/**\n * @param {number} type\n * @param {number} index\n * @return {number}\n */\nexport function commenter (type, index) {\n\twhile (next())\n\t\t// //\n\t\tif (type + character === 47 + 10)\n\t\t\tbreak\n\t\t// /*\n\t\telse if (type + character === 42 + 42 && peek() === 47)\n\t\t\tbreak\n\n\treturn '/*' + slice(index, position - 1) + '*' + from(type === 47 ? type : next())\n}\n\n/**\n * @param {number} index\n * @return {string}\n */\nexport function identifier (index) {\n\twhile (!token(peek()))\n\t\tnext()\n\n\treturn slice(index, position)\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAO,cAAc;AAE/E,OAAO,IAAIC,IAAI,GAAG,CAAC;AACnB,OAAO,IAAIC,MAAM,GAAG,CAAC;AACrB,OAAO,IAAIC,MAAM,GAAG,CAAC;AACrB,OAAO,IAAIC,QAAQ,GAAG,CAAC;AACvB,OAAO,IAAIC,SAAS,GAAG,CAAC;AACxB,OAAO,IAAIC,UAAU,GAAG,EAAE;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEV,MAAM,EAAE;EACzE,OAAO;IAACK,KAAK,EAAEA,KAAK;IAAEC,IAAI,EAAEA,IAAI;IAAEC,MAAM,EAAEA,MAAM;IAAEC,IAAI,EAAEA,IAAI;IAAEC,KAAK,EAAEA,KAAK;IAAEC,QAAQ,EAAEA,QAAQ;IAAEZ,IAAI,EAAEA,IAAI;IAAEC,MAAM,EAAEA,MAAM;IAAEC,MAAM,EAAEA,MAAM;IAAEW,MAAM,EAAE;EAAE,CAAC;AACxJ;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAAEN,IAAI,EAAEG,KAAK,EAAE;EAClC,OAAOZ,MAAM,CAACO,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAEE,IAAI,EAAE;IAACN,MAAM,EAAE,CAACM,IAAI,CAACN;EAAM,CAAC,EAAES,KAAK,CAAC;AAC5F;;AAEA;AACA;AACA;AACA,OAAO,SAASI,IAAIA,CAAA,EAAI;EACvB,OAAOX,SAAS;AACjB;;AAEA;AACA;AACA;AACA,OAAO,SAASY,IAAIA,CAAA,EAAI;EACvBZ,SAAS,GAAGD,QAAQ,GAAG,CAAC,GAAGR,MAAM,CAACU,UAAU,EAAE,EAAEF,QAAQ,CAAC,GAAG,CAAC;EAE7D,IAAIF,MAAM,EAAE,EAAEG,SAAS,KAAK,EAAE,EAC7BH,MAAM,GAAG,CAAC,EAAED,IAAI,EAAE;EAEnB,OAAOI,SAAS;AACjB;;AAEA;AACA;AACA;AACA,OAAO,SAASa,IAAIA,CAAA,EAAI;EACvBb,SAAS,GAAGD,QAAQ,GAAGD,MAAM,GAAGP,MAAM,CAACU,UAAU,EAAEF,QAAQ,EAAE,CAAC,GAAG,CAAC;EAElE,IAAIF,MAAM,EAAE,EAAEG,SAAS,KAAK,EAAE,EAC7BH,MAAM,GAAG,CAAC,EAAED,IAAI,EAAE;EAEnB,OAAOI,SAAS;AACjB;;AAEA;AACA;AACA;AACA,OAAO,SAASc,IAAIA,CAAA,EAAI;EACvB,OAAOvB,MAAM,CAACU,UAAU,EAAEF,QAAQ,CAAC;AACpC;;AAEA;AACA;AACA;AACA,OAAO,SAASgB,KAAKA,CAAA,EAAI;EACxB,OAAOhB,QAAQ;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,KAAKA,CAAEC,KAAK,EAAEC,GAAG,EAAE;EAClC,OAAOzB,MAAM,CAACQ,UAAU,EAAEgB,KAAK,EAAEC,GAAG,CAAC;AACtC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,KAAKA,CAAEb,IAAI,EAAE;EAC5B,QAAQA,IAAI;IACX;IACA,KAAK,CAAC;IAAE,KAAK,CAAC;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;MACxC,OAAO,CAAC;IACT;IACA,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,GAAG;IAC9D;IACA,KAAK,EAAE;IAAE,KAAK,GAAG;IAAE,KAAK,GAAG;MAC1B,OAAO,CAAC;IACT;IACA,KAAK,EAAE;MACN,OAAO,CAAC;IACT;IACA,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;IAAE,KAAK,EAAE;MACjC,OAAO,CAAC;IACT;IACA,KAAK,EAAE;IAAE,KAAK,EAAE;MACf,OAAO,CAAC;EACV;EAEA,OAAO,CAAC;AACT;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASc,KAAKA,CAAEjB,KAAK,EAAE;EAC7B,OAAOP,IAAI,GAAGC,MAAM,GAAG,CAAC,EAAEC,MAAM,GAAGN,MAAM,CAACS,UAAU,GAAGE,KAAK,CAAC,EAAEJ,QAAQ,GAAG,CAAC,EAAE,EAAE;AAChF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASsB,OAAOA,CAAElB,KAAK,EAAE;EAC/B,OAAOF,UAAU,GAAG,EAAE,EAAEE,KAAK;AAC9B;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASmB,OAAOA,CAAEhB,IAAI,EAAE;EAC9B,OAAOhB,IAAI,CAAC0B,KAAK,CAACjB,QAAQ,GAAG,CAAC,EAAEwB,SAAS,CAACjB,IAAI,KAAK,EAAE,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAI,KAAK,EAAE,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC;AACpG;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASkB,QAAQA,CAAErB,KAAK,EAAE;EAChC,OAAOkB,OAAO,CAACI,SAAS,CAACL,KAAK,CAACjB,KAAK,CAAC,CAAC,CAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASuB,UAAUA,CAAEpB,IAAI,EAAE;EACjC,OAAON,SAAS,GAAGc,IAAI,CAAC,CAAC,EACxB,IAAId,SAAS,GAAG,EAAE,EACjBa,IAAI,CAAC,CAAC,MAEN;EAEF,OAAOM,KAAK,CAACb,IAAI,CAAC,GAAG,CAAC,IAAIa,KAAK,CAACnB,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG;AAC1D;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASyB,SAASA,CAAEjB,QAAQ,EAAE;EACpC,OAAOK,IAAI,CAAC,CAAC,EACZ,QAAQM,KAAK,CAACnB,SAAS,CAAC;IACvB,KAAK,CAAC;MAAEN,MAAM,CAACiC,UAAU,CAAC5B,QAAQ,GAAG,CAAC,CAAC,EAAES,QAAQ,CAAC;MACjD;IACD,KAAK,CAAC;MAAEd,MAAM,CAAC4B,OAAO,CAACtB,SAAS,CAAC,EAAEQ,QAAQ,CAAC;MAC3C;IACD;MAASd,MAAM,CAACL,IAAI,CAACW,SAAS,CAAC,EAAEQ,QAAQ,CAAC;EAC3C;EAED,OAAOA,QAAQ;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASoB,QAAQA,CAAEC,KAAK,EAAEC,KAAK,EAAE;EACvC,OAAO,EAAEA,KAAK,IAAIjB,IAAI,CAAC,CAAC;EACvB;EACA,IAAIb,SAAS,GAAG,EAAE,IAAIA,SAAS,GAAG,GAAG,IAAKA,SAAS,GAAG,EAAE,IAAIA,SAAS,GAAG,EAAG,IAAKA,SAAS,GAAG,EAAE,IAAIA,SAAS,GAAG,EAAG,EAChH;EAEF,OAAOgB,KAAK,CAACa,KAAK,EAAEd,KAAK,CAAC,CAAC,IAAIe,KAAK,GAAG,CAAC,IAAIhB,IAAI,CAAC,CAAC,IAAI,EAAE,IAAID,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAC3E;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASU,SAASA,CAAEjB,IAAI,EAAE;EAChC,OAAOO,IAAI,CAAC,CAAC,EACZ,QAAQb,SAAS;IAChB;IACA,KAAKM,IAAI;MACR,OAAOP,QAAQ;IAChB;IACA,KAAK,EAAE;IAAE,KAAK,EAAE;MACf,IAAIO,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,EAAE,EAC7BiB,SAAS,CAACvB,SAAS,CAAC;MACrB;IACD;IACA,KAAK,EAAE;MACN,IAAIM,IAAI,KAAK,EAAE,EACdiB,SAAS,CAACjB,IAAI,CAAC;MAChB;IACD;IACA,KAAK,EAAE;MACNO,IAAI,CAAC,CAAC;MACN;EACF;EAED,OAAOd,QAAQ;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASgC,SAASA,CAAEzB,IAAI,EAAEuB,KAAK,EAAE;EACvC,OAAOhB,IAAI,CAAC,CAAC;EACZ;EACA,IAAIP,IAAI,GAAGN,SAAS,KAAK,EAAE,GAAG,EAAE,EAC/B;EACD;EAAA,KACK,IAAIM,IAAI,GAAGN,SAAS,KAAK,EAAE,GAAG,EAAE,IAAIc,IAAI,CAAC,CAAC,KAAK,EAAE,EACrD;EAEF,OAAO,IAAI,GAAGE,KAAK,CAACa,KAAK,EAAE9B,QAAQ,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGV,IAAI,CAACiB,IAAI,KAAK,EAAE,GAAGA,IAAI,GAAGO,IAAI,CAAC,CAAC,CAAC;AACnF;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASc,UAAUA,CAAEE,KAAK,EAAE;EAClC,OAAO,CAACV,KAAK,CAACL,IAAI,CAAC,CAAC,CAAC,EACpBD,IAAI,CAAC,CAAC;EAEP,OAAOG,KAAK,CAACa,KAAK,EAAE9B,QAAQ,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}