[{"D:\\Project\\salon-management-system\\src\\index.js": "1", "D:\\Project\\salon-management-system\\src\\reportWebVitals.js": "2", "D:\\Project\\salon-management-system\\src\\App.js": "3", "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js": "4", "D:\\Project\\salon-management-system\\src\\components\\Navbar.js": "5", "D:\\Project\\salon-management-system\\src\\components\\Appointments.js": "6", "D:\\Project\\salon-management-system\\src\\components\\Staff.js": "7", "D:\\Project\\salon-management-system\\src\\components\\Reports.js": "8", "D:\\Project\\salon-management-system\\src\\components\\Customers.js": "9", "D:\\Project\\salon-management-system\\src\\components\\Services.js": "10", "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js": "11", "D:\\Project\\salon-management-system\\src\\components\\Register.js": "12", "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js": "13", "D:\\Project\\salon-management-system\\src\\components\\Login.js": "14", "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js": "15", "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js": "16", "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js": "17", "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js": "18", "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js": "19", "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js": "20", "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js": "21", "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js": "22", "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js": "23"}, {"size": 535, "mtime": 1752673505339, "results": "24", "hashOfConfig": "25"}, {"size": 362, "mtime": 1752673505478, "results": "26", "hashOfConfig": "25"}, {"size": 3540, "mtime": 1752676443684, "results": "27", "hashOfConfig": "25"}, {"size": 14201, "mtime": 1752676565731, "results": "28", "hashOfConfig": "25"}, {"size": 7082, "mtime": 1752676396817, "results": "29", "hashOfConfig": "25"}, {"size": 14688, "mtime": 1752679633695, "results": "30", "hashOfConfig": "25"}, {"size": 15856, "mtime": 1752674665958, "results": "31", "hashOfConfig": "25"}, {"size": 15811, "mtime": 1752674110853, "results": "32", "hashOfConfig": "25"}, {"size": 12999, "mtime": 1752674653563, "results": "33", "hashOfConfig": "25"}, {"size": 13433, "mtime": 1752673981545, "results": "34", "hashOfConfig": "25"}, {"size": 4536, "mtime": 1752676872090, "results": "35", "hashOfConfig": "25"}, {"size": 9380, "mtime": 1752676890351, "results": "36", "hashOfConfig": "25"}, {"size": 9119, "mtime": 1752676328242, "results": "37", "hashOfConfig": "25"}, {"size": 6463, "mtime": 1752676861107, "results": "38", "hashOfConfig": "25"}, {"size": 5167, "mtime": 1752677065353, "results": "39", "hashOfConfig": "25"}, {"size": 10537, "mtime": 1752678651305, "results": "40", "hashOfConfig": "25"}, {"size": 2694, "mtime": 1752677885241, "results": "41", "hashOfConfig": "25"}, {"size": 6927, "mtime": 1752677912052, "results": "42", "hashOfConfig": "25"}, {"size": 9242, "mtime": 1752679654193, "results": "43", "hashOfConfig": "25"}, {"size": 9258, "mtime": 1752677718057, "results": "44", "hashOfConfig": "25"}, {"size": 8984, "mtime": 1752677761823, "results": "45", "hashOfConfig": "25"}, {"size": 9080, "mtime": 1752677807251, "results": "46", "hashOfConfig": "25"}, {"size": 12205, "mtime": 1752677860885, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wsws0p", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Project\\salon-management-system\\src\\index.js", [], [], "D:\\Project\\salon-management-system\\src\\reportWebVitals.js", [], [], "D:\\Project\\salon-management-system\\src\\App.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Dashboard.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Navbar.js", ["117", "118", "119"], [], "D:\\Project\\salon-management-system\\src\\components\\Appointments.js", ["120"], [], "D:\\Project\\salon-management-system\\src\\components\\Staff.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Reports.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Customers.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Services.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\ProtectedRoute.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Register.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\UserProfile.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\Login.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\AuthContext.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CalendarView.js", ["121"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingFlow.js", [], [], "D:\\Project\\salon-management-system\\src\\components\\CancelBookingDialog.js", [], [], "D:\\Project\\salon-management-system\\src\\contexts\\BookingContext.js", ["122", "123", "124", "125", "126"], [], "D:\\Project\\salon-management-system\\src\\components\\ServiceSelection.js", ["127"], [], "D:\\Project\\salon-management-system\\src\\components\\StylistSelection.js", ["128", "129", "130", "131"], [], "D:\\Project\\salon-management-system\\src\\components\\TimeSlotSelection.js", ["132", "133"], [], "D:\\Project\\salon-management-system\\src\\components\\BookingConfirmation.js", ["134", "135", "136", "137"], [], {"ruleId": "138", "severity": 1, "message": "139", "line": 35, "column": 25, "nodeType": "140", "messageId": "141", "endLine": 35, "endColumn": 32}, {"ruleId": "138", "severity": 1, "message": "142", "line": 35, "column": 34, "nodeType": "140", "messageId": "141", "endLine": 35, "endColumn": 41}, {"ruleId": "138", "severity": 1, "message": "143", "line": 35, "column": 43, "nodeType": "140", "messageId": "141", "endLine": 35, "endColumn": 53}, {"ruleId": "138", "severity": 1, "message": "144", "line": 26, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 26, "endColumn": 8}, {"ruleId": "138", "severity": 1, "message": "145", "line": 18, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 18, "endColumn": 10}, {"ruleId": "138", "severity": 1, "message": "146", "line": 2, "column": 30, "nodeType": "140", "messageId": "141", "endLine": 2, "endColumn": 37}, {"ruleId": "138", "severity": 1, "message": "147", "line": 2, "column": 39, "nodeType": "140", "messageId": "141", "endLine": 2, "endColumn": 47}, {"ruleId": "138", "severity": 1, "message": "148", "line": 2, "column": 49, "nodeType": "140", "messageId": "141", "endLine": 2, "endColumn": 58}, {"ruleId": "138", "severity": 1, "message": "149", "line": 2, "column": 60, "nodeType": "140", "messageId": "141", "endLine": 2, "endColumn": 68}, {"ruleId": "150", "severity": 1, "message": "151", "line": 264, "column": 47, "nodeType": "152", "messageId": "153", "endLine": 277, "endColumn": 10}, {"ruleId": "138", "severity": 1, "message": "154", "line": 25, "column": 14, "nodeType": "140", "messageId": "141", "endLine": 25, "endColumn": 23}, {"ruleId": "138", "severity": 1, "message": "155", "line": 13, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 13, "endColumn": 7}, {"ruleId": "138", "severity": 1, "message": "156", "line": 14, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 14, "endColumn": 11}, {"ruleId": "138", "severity": 1, "message": "157", "line": 15, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 15, "endColumn": 15}, {"ruleId": "138", "severity": 1, "message": "158", "line": 21, "column": 11, "nodeType": "140", "messageId": "141", "endLine": 21, "endColumn": 19}, {"ruleId": "138", "severity": 1, "message": "159", "line": 9, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 9, "endColumn": 7}, {"ruleId": "138", "severity": 1, "message": "160", "line": 13, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 13, "endColumn": 10}, {"ruleId": "138", "severity": 1, "message": "161", "line": 6, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 6, "endColumn": 7}, {"ruleId": "138", "severity": 1, "message": "162", "line": 7, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 7, "endColumn": 14}, {"ruleId": "138", "severity": 1, "message": "159", "line": 14, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 14, "endColumn": 7}, {"ruleId": "138", "severity": 1, "message": "163", "line": 25, "column": 18, "nodeType": "140", "messageId": "141", "endLine": 25, "endColumn": 27}, "no-unused-vars", "'isAdmin' is assigned a value but never used.", "Identifier", "unusedVar", "'isStaff' is assigned a value but never used.", "'isCustomer' is assigned a value but never used.", "'Alert' is defined but never used.", "'Tooltip' is defined but never used.", "'isAfter' is defined but never used.", "'isBefore' is defined but never used.", "'isSameDay' is defined but never used.", "'parseISO' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentTime', 'currentTime', 'currentTime'.", "ArrowFunctionExpression", "unsafeRefs", "'ColorIcon' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'StarIcon' is defined but never used.", "'Chip' is defined but never used.", "'Divider' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'MoneyIcon' is defined but never used."]