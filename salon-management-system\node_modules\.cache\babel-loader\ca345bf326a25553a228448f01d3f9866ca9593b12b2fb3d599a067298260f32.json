{"ast": null, "code": "import React from 'react';\nvar DropdownContext = /*#__PURE__*/React.createContext(null);\nexport default DropdownContext;", "map": {"version": 3, "names": ["React", "DropdownContext", "createContext"], "sources": ["D:/Project/salon-management-system/node_modules/react-overlays/esm/DropdownContext.js"], "sourcesContent": ["import React from 'react';\nvar DropdownContext = /*#__PURE__*/React.createContext(null);\nexport default DropdownContext;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,IAAIC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC5D,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}