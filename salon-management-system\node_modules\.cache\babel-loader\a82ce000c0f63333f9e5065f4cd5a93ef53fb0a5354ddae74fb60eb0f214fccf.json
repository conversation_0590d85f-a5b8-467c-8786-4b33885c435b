{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useTimeout, { Timeout } from '@mui/utils/useTimeout';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport Grow from \"../Grow/index.js\";\nimport Popper from \"../Popper/index.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport useId from \"../utils/useId.js\";\nimport useControlled from \"../utils/useControlled.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport tooltipClasses, { getTooltipUtilityClass } from \"./tooltipClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction round(value) {\n  return Math.round(value * 1e5) / 1e5;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableInteractive,\n    arrow,\n    touch,\n    placement\n  } = ownerState;\n  const slots = {\n    popper: ['popper', !disableInteractive && 'popperInteractive', arrow && 'popperArrow'],\n    tooltip: ['tooltip', arrow && 'tooltipArrow', touch && 'touch', `tooltipPlacement${capitalize(placement.split('-')[0])}`],\n    arrow: ['arrow']\n  };\n  return composeClasses(slots, getTooltipUtilityClass, classes);\n};\nconst TooltipPopper = styled(Popper, {\n  name: 'MuiTooltip',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popper, !ownerState.disableInteractive && styles.popperInteractive, ownerState.arrow && styles.popperArrow, !ownerState.open && styles.popperClose];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.tooltip,\n  pointerEvents: 'none',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disableInteractive,\n    style: {\n      pointerEvents: 'auto'\n    }\n  }, {\n    props: ({\n      open\n    }) => !open,\n    style: {\n      pointerEvents: 'none'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow,\n    style: {\n      [`&[data-popper-placement*=\"bottom\"] .${tooltipClasses.arrow}`]: {\n        top: 0,\n        marginTop: '-0.71em',\n        '&::before': {\n          transformOrigin: '0 100%'\n        }\n      },\n      [`&[data-popper-placement*=\"top\"] .${tooltipClasses.arrow}`]: {\n        bottom: 0,\n        marginBottom: '-0.71em',\n        '&::before': {\n          transformOrigin: '100% 0'\n        }\n      },\n      [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: {\n        height: '1em',\n        width: '0.71em',\n        '&::before': {\n          transformOrigin: '100% 100%'\n        }\n      },\n      [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: {\n        height: '1em',\n        width: '0.71em',\n        '&::before': {\n          transformOrigin: '0 0'\n        }\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow && !ownerState.isRtl,\n    style: {\n      [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: {\n        left: 0,\n        marginLeft: '-0.71em'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow && !!ownerState.isRtl,\n    style: {\n      [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: {\n        right: 0,\n        marginRight: '-0.71em'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow && !ownerState.isRtl,\n    style: {\n      [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: {\n        right: 0,\n        marginRight: '-0.71em'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow && !!ownerState.isRtl,\n    style: {\n      [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: {\n        left: 0,\n        marginLeft: '-0.71em'\n      }\n    }\n  }]\n})));\nconst TooltipTooltip = styled('div', {\n  name: 'MuiTooltip',\n  slot: 'Tooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.tooltip, ownerState.touch && styles.touch, ownerState.arrow && styles.tooltipArrow, styles[`tooltipPlacement${capitalize(ownerState.placement.split('-')[0])}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  backgroundColor: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.92),\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  color: (theme.vars || theme).palette.common.white,\n  fontFamily: theme.typography.fontFamily,\n  padding: '4px 8px',\n  fontSize: theme.typography.pxToRem(11),\n  maxWidth: 300,\n  margin: 2,\n  wordWrap: 'break-word',\n  fontWeight: theme.typography.fontWeightMedium,\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n    transformOrigin: 'right center'\n  },\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n    transformOrigin: 'left center'\n  },\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"top\"] &`]: {\n    transformOrigin: 'center bottom',\n    marginBottom: '14px'\n  },\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"bottom\"] &`]: {\n    transformOrigin: 'center top',\n    marginTop: '14px'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.arrow,\n    style: {\n      position: 'relative',\n      margin: 0\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.touch,\n    style: {\n      padding: '8px 16px',\n      fontSize: theme.typography.pxToRem(14),\n      lineHeight: `${round(16 / 14)}em`,\n      fontWeight: theme.typography.fontWeightRegular\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.isRtl,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n        marginRight: '14px'\n      },\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n        marginLeft: '14px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.isRtl && ownerState.touch,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n        marginRight: '24px'\n      },\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n        marginLeft: '24px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.isRtl,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n        marginLeft: '14px'\n      },\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n        marginRight: '14px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.isRtl && ownerState.touch,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n        marginLeft: '24px'\n      },\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n        marginRight: '24px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.touch,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"top\"] &`]: {\n        marginBottom: '24px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.touch,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"bottom\"] &`]: {\n        marginTop: '24px'\n      }\n    }\n  }]\n})));\nconst TooltipArrow = styled('span', {\n  name: 'MuiTooltip',\n  slot: 'Arrow'\n})(memoTheme(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  position: 'absolute',\n  width: '1em',\n  height: '0.71em' /* = width / sqrt(2) = (length of the hypotenuse) */,\n  boxSizing: 'border-box',\n  color: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.9),\n  '&::before': {\n    content: '\"\"',\n    margin: 'auto',\n    display: 'block',\n    width: '100%',\n    height: '100%',\n    backgroundColor: 'currentColor',\n    transform: 'rotate(45deg)'\n  }\n})));\nlet hystersisOpen = false;\nconst hystersisTimer = new Timeout();\nlet cursorPosition = {\n  x: 0,\n  y: 0\n};\nexport function testReset() {\n  hystersisOpen = false;\n  hystersisTimer.clear();\n}\nfunction composeEventHandler(handler, eventHandler) {\n  return (event, ...params) => {\n    if (eventHandler) {\n      eventHandler(event, ...params);\n    }\n    handler(event, ...params);\n  };\n}\n\n// TODO v6: Remove PopperComponent, PopperProps, TransitionComponent and TransitionProps.\nconst Tooltip = /*#__PURE__*/React.forwardRef(function Tooltip(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTooltip'\n  });\n  const {\n    arrow = false,\n    children: childrenProp,\n    classes: classesProp,\n    components = {},\n    componentsProps = {},\n    describeChild = false,\n    disableFocusListener = false,\n    disableHoverListener = false,\n    disableInteractive: disableInteractiveProp = false,\n    disableTouchListener = false,\n    enterDelay = 100,\n    enterNextDelay = 0,\n    enterTouchDelay = 700,\n    followCursor = false,\n    id: idProp,\n    leaveDelay = 0,\n    leaveTouchDelay = 1500,\n    onClose,\n    onOpen,\n    open: openProp,\n    placement = 'bottom',\n    PopperComponent: PopperComponentProp,\n    PopperProps = {},\n    slotProps = {},\n    slots = {},\n    title,\n    TransitionComponent: TransitionComponentProp,\n    TransitionProps,\n    ...other\n  } = props;\n\n  // to prevent runtime errors, developers will need to provide a child as a React element anyway.\n  const children = /*#__PURE__*/React.isValidElement(childrenProp) ? childrenProp : /*#__PURE__*/_jsx(\"span\", {\n    children: childrenProp\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const [childNode, setChildNode] = React.useState();\n  const [arrowRef, setArrowRef] = React.useState(null);\n  const ignoreNonTouchEvents = React.useRef(false);\n  const disableInteractive = disableInteractiveProp || followCursor;\n  const closeTimer = useTimeout();\n  const enterTimer = useTimeout();\n  const leaveTimer = useTimeout();\n  const touchTimer = useTimeout();\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'Tooltip',\n    state: 'open'\n  });\n  let open = openState;\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- process.env never changes\n    const {\n      current: isControlled\n    } = React.useRef(openProp !== undefined);\n\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- process.env never changes\n    React.useEffect(() => {\n      if (childNode && childNode.disabled && !isControlled && title !== '' && childNode.tagName.toLowerCase() === 'button') {\n        console.warn(['MUI: You are providing a disabled `button` child to the Tooltip component.', 'A disabled element does not fire events.', \"Tooltip needs to listen to the child element's events to display the title.\", '', 'Add a simple wrapper element, such as a `span`.'].join('\\n'));\n      }\n    }, [title, childNode, isControlled]);\n  }\n  const id = useId(idProp);\n  const prevUserSelect = React.useRef();\n  const stopTouchInteraction = useEventCallback(() => {\n    if (prevUserSelect.current !== undefined) {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      prevUserSelect.current = undefined;\n    }\n    touchTimer.clear();\n  });\n  React.useEffect(() => stopTouchInteraction, [stopTouchInteraction]);\n  const handleOpen = event => {\n    hystersisTimer.clear();\n    hystersisOpen = true;\n\n    // The mouseover event will trigger for every nested element in the tooltip.\n    // We can skip rerendering when the tooltip is already open.\n    // We are using the mouseover event instead of the mouseenter event to fix a hide/show issue.\n    setOpenState(true);\n    if (onOpen && !open) {\n      onOpen(event);\n    }\n  };\n  const handleClose = useEventCallback(\n  /**\n   * @param {React.SyntheticEvent | Event} event\n   */\n  event => {\n    hystersisTimer.start(800 + leaveDelay, () => {\n      hystersisOpen = false;\n    });\n    setOpenState(false);\n    if (onClose && open) {\n      onClose(event);\n    }\n    closeTimer.start(theme.transitions.duration.shortest, () => {\n      ignoreNonTouchEvents.current = false;\n    });\n  });\n  const handleMouseOver = event => {\n    if (ignoreNonTouchEvents.current && event.type !== 'touchstart') {\n      return;\n    }\n\n    // Remove the title ahead of time.\n    // We don't want to wait for the next render commit.\n    // We would risk displaying two tooltips at the same time (native + this one).\n    if (childNode) {\n      childNode.removeAttribute('title');\n    }\n    enterTimer.clear();\n    leaveTimer.clear();\n    if (enterDelay || hystersisOpen && enterNextDelay) {\n      enterTimer.start(hystersisOpen ? enterNextDelay : enterDelay, () => {\n        handleOpen(event);\n      });\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleMouseLeave = event => {\n    enterTimer.clear();\n    leaveTimer.start(leaveDelay, () => {\n      handleClose(event);\n    });\n  };\n  const [, setChildIsFocusVisible] = React.useState(false);\n  const handleBlur = event => {\n    if (!isFocusVisible(event.target)) {\n      setChildIsFocusVisible(false);\n      handleMouseLeave(event);\n    }\n  };\n  const handleFocus = event => {\n    // Workaround for https://github.com/facebook/react/issues/7769\n    // The autoFocus of React might trigger the event before the componentDidMount.\n    // We need to account for this eventuality.\n    if (!childNode) {\n      setChildNode(event.currentTarget);\n    }\n    if (isFocusVisible(event.target)) {\n      setChildIsFocusVisible(true);\n      handleMouseOver(event);\n    }\n  };\n  const detectTouchStart = event => {\n    ignoreNonTouchEvents.current = true;\n    const childrenProps = children.props;\n    if (childrenProps.onTouchStart) {\n      childrenProps.onTouchStart(event);\n    }\n  };\n  const handleTouchStart = event => {\n    detectTouchStart(event);\n    leaveTimer.clear();\n    closeTimer.clear();\n    stopTouchInteraction();\n    prevUserSelect.current = document.body.style.WebkitUserSelect;\n    // Prevent iOS text selection on long-tap.\n    document.body.style.WebkitUserSelect = 'none';\n    touchTimer.start(enterTouchDelay, () => {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      handleMouseOver(event);\n    });\n  };\n  const handleTouchEnd = event => {\n    if (children.props.onTouchEnd) {\n      children.props.onTouchEnd(event);\n    }\n    stopTouchInteraction();\n    leaveTimer.start(leaveTouchDelay, () => {\n      handleClose(event);\n    });\n  };\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (nativeEvent.key === 'Escape') {\n        handleClose(nativeEvent);\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleClose, open]);\n  const handleRef = useForkRef(getReactElementRef(children), setChildNode, ref);\n\n  // There is no point in displaying an empty tooltip.\n  // So we exclude all falsy values, except 0, which is valid.\n  if (!title && title !== 0) {\n    open = false;\n  }\n  const popperRef = React.useRef();\n  const handleMouseMove = event => {\n    const childrenProps = children.props;\n    if (childrenProps.onMouseMove) {\n      childrenProps.onMouseMove(event);\n    }\n    cursorPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    if (popperRef.current) {\n      popperRef.current.update();\n    }\n  };\n  const nameOrDescProps = {};\n  const titleIsString = typeof title === 'string';\n  if (describeChild) {\n    nameOrDescProps.title = !open && titleIsString && !disableHoverListener ? title : null;\n    nameOrDescProps['aria-describedby'] = open ? id : null;\n  } else {\n    nameOrDescProps['aria-label'] = titleIsString ? title : null;\n    nameOrDescProps['aria-labelledby'] = open && !titleIsString ? id : null;\n  }\n  const childrenProps = {\n    ...nameOrDescProps,\n    ...other,\n    ...children.props,\n    className: clsx(other.className, children.props.className),\n    onTouchStart: detectTouchStart,\n    ref: handleRef,\n    ...(followCursor ? {\n      onMouseMove: handleMouseMove\n    } : {})\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    childrenProps['data-mui-internal-clone-element'] = true;\n\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- process.env never changes\n    React.useEffect(() => {\n      if (childNode && !childNode.getAttribute('data-mui-internal-clone-element')) {\n        console.error(['MUI: The `children` component of the Tooltip is not forwarding its props correctly.', 'Please make sure that props are spread on the same element that the ref is applied to.'].join('\\n'));\n      }\n    }, [childNode]);\n  }\n  const interactiveWrapperListeners = {};\n  if (!disableTouchListener) {\n    childrenProps.onTouchStart = handleTouchStart;\n    childrenProps.onTouchEnd = handleTouchEnd;\n  }\n  if (!disableHoverListener) {\n    childrenProps.onMouseOver = composeEventHandler(handleMouseOver, childrenProps.onMouseOver);\n    childrenProps.onMouseLeave = composeEventHandler(handleMouseLeave, childrenProps.onMouseLeave);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onMouseOver = handleMouseOver;\n      interactiveWrapperListeners.onMouseLeave = handleMouseLeave;\n    }\n  }\n  if (!disableFocusListener) {\n    childrenProps.onFocus = composeEventHandler(handleFocus, childrenProps.onFocus);\n    childrenProps.onBlur = composeEventHandler(handleBlur, childrenProps.onBlur);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onFocus = handleFocus;\n      interactiveWrapperListeners.onBlur = handleBlur;\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (children.props.title) {\n      console.error(['MUI: You have provided a `title` prop to the child of <Tooltip />.', `Remove this title prop \\`${children.props.title}\\` or the Tooltip component.`].join('\\n'));\n    }\n  }\n  const ownerState = {\n    ...props,\n    isRtl,\n    arrow,\n    disableInteractive,\n    placement,\n    PopperComponentProp,\n    touch: ignoreNonTouchEvents.current\n  };\n  const resolvedPopperProps = typeof slotProps.popper === 'function' ? slotProps.popper(ownerState) : slotProps.popper;\n  const popperOptions = React.useMemo(() => {\n    let tooltipModifiers = [{\n      name: 'arrow',\n      enabled: Boolean(arrowRef),\n      options: {\n        element: arrowRef,\n        padding: 4\n      }\n    }];\n    if (PopperProps.popperOptions?.modifiers) {\n      tooltipModifiers = tooltipModifiers.concat(PopperProps.popperOptions.modifiers);\n    }\n    if (resolvedPopperProps?.popperOptions?.modifiers) {\n      tooltipModifiers = tooltipModifiers.concat(resolvedPopperProps.popperOptions.modifiers);\n    }\n    return {\n      ...PopperProps.popperOptions,\n      ...resolvedPopperProps?.popperOptions,\n      modifiers: tooltipModifiers\n    };\n  }, [arrowRef, PopperProps.popperOptions, resolvedPopperProps?.popperOptions]);\n  const classes = useUtilityClasses(ownerState);\n  const resolvedTransitionProps = typeof slotProps.transition === 'function' ? slotProps.transition(ownerState) : slotProps.transition;\n  const externalForwardedProps = {\n    slots: {\n      popper: components.Popper,\n      transition: components.Transition ?? TransitionComponentProp,\n      tooltip: components.Tooltip,\n      arrow: components.Arrow,\n      ...slots\n    },\n    slotProps: {\n      arrow: slotProps.arrow ?? componentsProps.arrow,\n      popper: {\n        ...PopperProps,\n        ...(resolvedPopperProps ?? componentsProps.popper)\n      },\n      // resolvedPopperProps can be spread because it's already an object\n      tooltip: slotProps.tooltip ?? componentsProps.tooltip,\n      transition: {\n        ...TransitionProps,\n        ...(resolvedTransitionProps ?? componentsProps.transition)\n      }\n    }\n  };\n  const [PopperSlot, popperSlotProps] = useSlot('popper', {\n    elementType: TooltipPopper,\n    externalForwardedProps,\n    ownerState,\n    className: clsx(classes.popper, PopperProps?.className)\n  });\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Grow,\n    externalForwardedProps,\n    ownerState\n  });\n  const [TooltipSlot, tooltipSlotProps] = useSlot('tooltip', {\n    elementType: TooltipTooltip,\n    className: classes.tooltip,\n    externalForwardedProps,\n    ownerState\n  });\n  const [ArrowSlot, arrowSlotProps] = useSlot('arrow', {\n    elementType: TooltipArrow,\n    className: classes.arrow,\n    externalForwardedProps,\n    ownerState,\n    ref: setArrowRef\n  });\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/React.cloneElement(children, childrenProps), /*#__PURE__*/_jsx(PopperSlot, {\n      as: PopperComponentProp ?? Popper,\n      placement: placement,\n      anchorEl: followCursor ? {\n        getBoundingClientRect: () => ({\n          top: cursorPosition.y,\n          left: cursorPosition.x,\n          right: cursorPosition.x,\n          bottom: cursorPosition.y,\n          width: 0,\n          height: 0\n        })\n      } : childNode,\n      popperRef: popperRef,\n      open: childNode ? open : false,\n      id: id,\n      transition: true,\n      ...interactiveWrapperListeners,\n      ...popperSlotProps,\n      popperOptions: popperOptions,\n      children: ({\n        TransitionProps: TransitionPropsInner\n      }) => /*#__PURE__*/_jsx(TransitionSlot, {\n        timeout: theme.transitions.duration.shorter,\n        ...TransitionPropsInner,\n        ...transitionSlotProps,\n        children: /*#__PURE__*/_jsxs(TooltipSlot, {\n          ...tooltipSlotProps,\n          children: [title, arrow ? /*#__PURE__*/_jsx(ArrowSlot, {\n            ...arrowSlotProps\n          }) : null]\n        })\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tooltip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, adds an arrow to the tooltip.\n   * @default false\n   */\n  arrow: PropTypes.bool,\n  /**\n   * Tooltip reference element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Arrow: PropTypes.elementType,\n    Popper: PropTypes.elementType,\n    Tooltip: PropTypes.elementType,\n    Transition: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * Set to `true` if the `title` acts as an accessible description.\n   * By default the `title` acts as an accessible label for the child.\n   * @default false\n   */\n  describeChild: PropTypes.bool,\n  /**\n   * Do not respond to focus-visible events.\n   * @default false\n   */\n  disableFocusListener: PropTypes.bool,\n  /**\n   * Do not respond to hover events.\n   * @default false\n   */\n  disableHoverListener: PropTypes.bool,\n  /**\n   * Makes a tooltip not interactive, i.e. it will close when the user\n   * hovers over the tooltip before the `leaveDelay` is expired.\n   * @default false\n   */\n  disableInteractive: PropTypes.bool,\n  /**\n   * Do not respond to long press touch events.\n   * @default false\n   */\n  disableTouchListener: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before showing the tooltip.\n   * This prop won't impact the enter touch delay (`enterTouchDelay`).\n   * @default 100\n   */\n  enterDelay: PropTypes.number,\n  /**\n   * The number of milliseconds to wait before showing the tooltip when one was already recently opened.\n   * @default 0\n   */\n  enterNextDelay: PropTypes.number,\n  /**\n   * The number of milliseconds a user must touch the element before showing the tooltip.\n   * @default 700\n   */\n  enterTouchDelay: PropTypes.number,\n  /**\n   * If `true`, the tooltip follow the cursor over the wrapped element.\n   * @default false\n   */\n  followCursor: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * The number of milliseconds to wait before hiding the tooltip.\n   * This prop won't impact the leave touch delay (`leaveTouchDelay`).\n   * @default 0\n   */\n  leaveDelay: PropTypes.number,\n  /**\n   * The number of milliseconds after the user stops touching an element before hiding the tooltip.\n   * @default 1500\n   */\n  leaveTouchDelay: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * Tooltip placement.\n   * @default 'bottom'\n   */\n  placement: PropTypes.oneOf(['auto-end', 'auto-start', 'auto', 'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * The component used for the popper.\n   * @deprecated use the `slots.popper` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Popper`](https://mui.com/material-ui/api/popper/) element.\n   * @deprecated use the `slotProps.popper` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  PopperProps: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    arrow: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    tooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    arrow: PropTypes.elementType,\n    popper: PropTypes.elementType,\n    tooltip: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Tooltip title. Zero-length titles string, undefined, null and false are never displayed.\n   */\n  title: PropTypes.node,\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @deprecated use the `slots.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Tooltip;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "useTimeout", "Timeout", "elementAcceptingRef", "composeClasses", "alpha", "useRtl", "isFocusVisible", "getReactElementRef", "styled", "useTheme", "memoTheme", "useDefaultProps", "capitalize", "Grow", "<PERSON><PERSON>", "useEventCallback", "useForkRef", "useId", "useControlled", "useSlot", "tooltipClasses", "getTooltipUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "round", "value", "Math", "useUtilityClasses", "ownerState", "classes", "disableInteractive", "arrow", "touch", "placement", "slots", "popper", "tooltip", "split", "TooltipPopper", "name", "slot", "overridesResolver", "props", "styles", "popperInteractive", "popperArrow", "open", "popperClose", "theme", "zIndex", "vars", "pointerEvents", "variants", "style", "top", "marginTop", "transform<PERSON><PERSON>in", "bottom", "marginBottom", "height", "width", "isRtl", "left", "marginLeft", "right", "marginRight", "TooltipTooltip", "tooltipArrow", "backgroundColor", "palette", "<PERSON><PERSON><PERSON>", "bg", "grey", "borderRadius", "shape", "color", "common", "white", "fontFamily", "typography", "padding", "fontSize", "pxToRem", "max<PERSON><PERSON><PERSON>", "margin", "wordWrap", "fontWeight", "fontWeightMedium", "position", "lineHeight", "fontWeightRegular", "TooltipArrow", "overflow", "boxSizing", "content", "display", "transform", "hystersisOpen", "hystersis<PERSON><PERSON>r", "cursorPosition", "x", "y", "testReset", "clear", "composeEventHandler", "handler", "<PERSON><PERSON><PERSON><PERSON>", "event", "params", "forwardRef", "inProps", "ref", "children", "childrenProp", "classesProp", "components", "componentsProps", "<PERSON><PERSON><PERSON><PERSON>", "disableFocusListener", "disableHoverListener", "disableInteractiveProp", "disableTouch<PERSON><PERSON>ener", "enterDelay", "enterNextDelay", "enterTouchDelay", "followCursor", "id", "idProp", "leaveDelay", "leaveTouchDelay", "onClose", "onOpen", "openProp", "PopperComponent", "PopperComponentProp", "PopperProps", "slotProps", "title", "TransitionComponent", "TransitionComponentProp", "TransitionProps", "other", "isValidElement", "childNode", "setChildNode", "useState", "arrowRef", "setArrowRef", "ignoreNonTouchEvents", "useRef", "closeTimer", "enterTimer", "leaveTimer", "touchTimer", "openState", "setOpenState", "controlled", "default", "state", "process", "env", "NODE_ENV", "current", "isControlled", "undefined", "useEffect", "disabled", "tagName", "toLowerCase", "console", "warn", "join", "prevUserSelect", "stopTouchInteraction", "document", "body", "WebkitUserSelect", "handleOpen", "handleClose", "start", "transitions", "duration", "shortest", "handleMouseOver", "type", "removeAttribute", "handleMouseLeave", "setChildIsFocusVisible", "handleBlur", "target", "handleFocus", "currentTarget", "detectTouchStart", "childrenProps", "onTouchStart", "handleTouchStart", "handleTouchEnd", "onTouchEnd", "handleKeyDown", "nativeEvent", "key", "addEventListener", "removeEventListener", "handleRef", "popperRef", "handleMouseMove", "onMouseMove", "clientX", "clientY", "update", "nameOrDescProps", "titleIsString", "className", "getAttribute", "error", "interactiveWrapperListeners", "onMouseOver", "onMouseLeave", "onFocus", "onBlur", "resolvedPopperProps", "popperOptions", "useMemo", "tooltipModifiers", "enabled", "Boolean", "options", "element", "modifiers", "concat", "resolvedTransitionProps", "transition", "externalForwardedProps", "Transition", "Arrow", "PopperSlot", "popperSlotProps", "elementType", "TransitionSlot", "transitionSlotProps", "TooltipSlot", "tooltipSlotProps", "ArrowSlot", "arrowSlotProps", "Fragment", "cloneElement", "as", "anchorEl", "getBoundingClientRect", "TransitionPropsInner", "timeout", "shorter", "propTypes", "bool", "isRequired", "object", "string", "number", "func", "oneOf", "oneOfType", "sx", "arrayOf", "node"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/Tooltip/Tooltip.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport useTimeout, { Timeout } from '@mui/utils/useTimeout';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { useRtl } from '@mui/system/RtlProvider';\nimport isFocusVisible from '@mui/utils/isFocusVisible';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport Grow from \"../Grow/index.js\";\nimport Popper from \"../Popper/index.js\";\nimport useEventCallback from \"../utils/useEventCallback.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport useId from \"../utils/useId.js\";\nimport useControlled from \"../utils/useControlled.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport tooltipClasses, { getTooltipUtilityClass } from \"./tooltipClasses.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nfunction round(value) {\n  return Math.round(value * 1e5) / 1e5;\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disableInteractive,\n    arrow,\n    touch,\n    placement\n  } = ownerState;\n  const slots = {\n    popper: ['popper', !disableInteractive && 'popperInteractive', arrow && 'popperArrow'],\n    tooltip: ['tooltip', arrow && 'tooltipArrow', touch && 'touch', `tooltipPlacement${capitalize(placement.split('-')[0])}`],\n    arrow: ['arrow']\n  };\n  return composeClasses(slots, getTooltipUtilityClass, classes);\n};\nconst TooltipPopper = styled(Popper, {\n  name: 'MuiTooltip',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.popper, !ownerState.disableInteractive && styles.popperInteractive, ownerState.arrow && styles.popperArrow, !ownerState.open && styles.popperClose];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.tooltip,\n  pointerEvents: 'none',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disableInteractive,\n    style: {\n      pointerEvents: 'auto'\n    }\n  }, {\n    props: ({\n      open\n    }) => !open,\n    style: {\n      pointerEvents: 'none'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow,\n    style: {\n      [`&[data-popper-placement*=\"bottom\"] .${tooltipClasses.arrow}`]: {\n        top: 0,\n        marginTop: '-0.71em',\n        '&::before': {\n          transformOrigin: '0 100%'\n        }\n      },\n      [`&[data-popper-placement*=\"top\"] .${tooltipClasses.arrow}`]: {\n        bottom: 0,\n        marginBottom: '-0.71em',\n        '&::before': {\n          transformOrigin: '100% 0'\n        }\n      },\n      [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: {\n        height: '1em',\n        width: '0.71em',\n        '&::before': {\n          transformOrigin: '100% 100%'\n        }\n      },\n      [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: {\n        height: '1em',\n        width: '0.71em',\n        '&::before': {\n          transformOrigin: '0 0'\n        }\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow && !ownerState.isRtl,\n    style: {\n      [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: {\n        left: 0,\n        marginLeft: '-0.71em'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow && !!ownerState.isRtl,\n    style: {\n      [`&[data-popper-placement*=\"right\"] .${tooltipClasses.arrow}`]: {\n        right: 0,\n        marginRight: '-0.71em'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow && !ownerState.isRtl,\n    style: {\n      [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: {\n        right: 0,\n        marginRight: '-0.71em'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.arrow && !!ownerState.isRtl,\n    style: {\n      [`&[data-popper-placement*=\"left\"] .${tooltipClasses.arrow}`]: {\n        left: 0,\n        marginLeft: '-0.71em'\n      }\n    }\n  }]\n})));\nconst TooltipTooltip = styled('div', {\n  name: 'MuiTooltip',\n  slot: 'Tooltip',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.tooltip, ownerState.touch && styles.touch, ownerState.arrow && styles.tooltipArrow, styles[`tooltipPlacement${capitalize(ownerState.placement.split('-')[0])}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  backgroundColor: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.92),\n  borderRadius: (theme.vars || theme).shape.borderRadius,\n  color: (theme.vars || theme).palette.common.white,\n  fontFamily: theme.typography.fontFamily,\n  padding: '4px 8px',\n  fontSize: theme.typography.pxToRem(11),\n  maxWidth: 300,\n  margin: 2,\n  wordWrap: 'break-word',\n  fontWeight: theme.typography.fontWeightMedium,\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n    transformOrigin: 'right center'\n  },\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n    transformOrigin: 'left center'\n  },\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"top\"] &`]: {\n    transformOrigin: 'center bottom',\n    marginBottom: '14px'\n  },\n  [`.${tooltipClasses.popper}[data-popper-placement*=\"bottom\"] &`]: {\n    transformOrigin: 'center top',\n    marginTop: '14px'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.arrow,\n    style: {\n      position: 'relative',\n      margin: 0\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.touch,\n    style: {\n      padding: '8px 16px',\n      fontSize: theme.typography.pxToRem(14),\n      lineHeight: `${round(16 / 14)}em`,\n      fontWeight: theme.typography.fontWeightRegular\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.isRtl,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n        marginRight: '14px'\n      },\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n        marginLeft: '14px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.isRtl && ownerState.touch,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n        marginRight: '24px'\n      },\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n        marginLeft: '24px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.isRtl,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n        marginLeft: '14px'\n      },\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n        marginRight: '14px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.isRtl && ownerState.touch,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"left\"] &`]: {\n        marginLeft: '24px'\n      },\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"right\"] &`]: {\n        marginRight: '24px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.touch,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"top\"] &`]: {\n        marginBottom: '24px'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.touch,\n    style: {\n      [`.${tooltipClasses.popper}[data-popper-placement*=\"bottom\"] &`]: {\n        marginTop: '24px'\n      }\n    }\n  }]\n})));\nconst TooltipArrow = styled('span', {\n  name: 'MuiTooltip',\n  slot: 'Arrow'\n})(memoTheme(({\n  theme\n}) => ({\n  overflow: 'hidden',\n  position: 'absolute',\n  width: '1em',\n  height: '0.71em' /* = width / sqrt(2) = (length of the hypotenuse) */,\n  boxSizing: 'border-box',\n  color: theme.vars ? theme.vars.palette.Tooltip.bg : alpha(theme.palette.grey[700], 0.9),\n  '&::before': {\n    content: '\"\"',\n    margin: 'auto',\n    display: 'block',\n    width: '100%',\n    height: '100%',\n    backgroundColor: 'currentColor',\n    transform: 'rotate(45deg)'\n  }\n})));\nlet hystersisOpen = false;\nconst hystersisTimer = new Timeout();\nlet cursorPosition = {\n  x: 0,\n  y: 0\n};\nexport function testReset() {\n  hystersisOpen = false;\n  hystersisTimer.clear();\n}\nfunction composeEventHandler(handler, eventHandler) {\n  return (event, ...params) => {\n    if (eventHandler) {\n      eventHandler(event, ...params);\n    }\n    handler(event, ...params);\n  };\n}\n\n// TODO v6: Remove PopperComponent, PopperProps, TransitionComponent and TransitionProps.\nconst Tooltip = /*#__PURE__*/React.forwardRef(function Tooltip(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTooltip'\n  });\n  const {\n    arrow = false,\n    children: childrenProp,\n    classes: classesProp,\n    components = {},\n    componentsProps = {},\n    describeChild = false,\n    disableFocusListener = false,\n    disableHoverListener = false,\n    disableInteractive: disableInteractiveProp = false,\n    disableTouchListener = false,\n    enterDelay = 100,\n    enterNextDelay = 0,\n    enterTouchDelay = 700,\n    followCursor = false,\n    id: idProp,\n    leaveDelay = 0,\n    leaveTouchDelay = 1500,\n    onClose,\n    onOpen,\n    open: openProp,\n    placement = 'bottom',\n    PopperComponent: PopperComponentProp,\n    PopperProps = {},\n    slotProps = {},\n    slots = {},\n    title,\n    TransitionComponent: TransitionComponentProp,\n    TransitionProps,\n    ...other\n  } = props;\n\n  // to prevent runtime errors, developers will need to provide a child as a React element anyway.\n  const children = /*#__PURE__*/React.isValidElement(childrenProp) ? childrenProp : /*#__PURE__*/_jsx(\"span\", {\n    children: childrenProp\n  });\n  const theme = useTheme();\n  const isRtl = useRtl();\n  const [childNode, setChildNode] = React.useState();\n  const [arrowRef, setArrowRef] = React.useState(null);\n  const ignoreNonTouchEvents = React.useRef(false);\n  const disableInteractive = disableInteractiveProp || followCursor;\n  const closeTimer = useTimeout();\n  const enterTimer = useTimeout();\n  const leaveTimer = useTimeout();\n  const touchTimer = useTimeout();\n  const [openState, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: 'Tooltip',\n    state: 'open'\n  });\n  let open = openState;\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- process.env never changes\n    const {\n      current: isControlled\n    } = React.useRef(openProp !== undefined);\n\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- process.env never changes\n    React.useEffect(() => {\n      if (childNode && childNode.disabled && !isControlled && title !== '' && childNode.tagName.toLowerCase() === 'button') {\n        console.warn(['MUI: You are providing a disabled `button` child to the Tooltip component.', 'A disabled element does not fire events.', \"Tooltip needs to listen to the child element's events to display the title.\", '', 'Add a simple wrapper element, such as a `span`.'].join('\\n'));\n      }\n    }, [title, childNode, isControlled]);\n  }\n  const id = useId(idProp);\n  const prevUserSelect = React.useRef();\n  const stopTouchInteraction = useEventCallback(() => {\n    if (prevUserSelect.current !== undefined) {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      prevUserSelect.current = undefined;\n    }\n    touchTimer.clear();\n  });\n  React.useEffect(() => stopTouchInteraction, [stopTouchInteraction]);\n  const handleOpen = event => {\n    hystersisTimer.clear();\n    hystersisOpen = true;\n\n    // The mouseover event will trigger for every nested element in the tooltip.\n    // We can skip rerendering when the tooltip is already open.\n    // We are using the mouseover event instead of the mouseenter event to fix a hide/show issue.\n    setOpenState(true);\n    if (onOpen && !open) {\n      onOpen(event);\n    }\n  };\n  const handleClose = useEventCallback(\n  /**\n   * @param {React.SyntheticEvent | Event} event\n   */\n  event => {\n    hystersisTimer.start(800 + leaveDelay, () => {\n      hystersisOpen = false;\n    });\n    setOpenState(false);\n    if (onClose && open) {\n      onClose(event);\n    }\n    closeTimer.start(theme.transitions.duration.shortest, () => {\n      ignoreNonTouchEvents.current = false;\n    });\n  });\n  const handleMouseOver = event => {\n    if (ignoreNonTouchEvents.current && event.type !== 'touchstart') {\n      return;\n    }\n\n    // Remove the title ahead of time.\n    // We don't want to wait for the next render commit.\n    // We would risk displaying two tooltips at the same time (native + this one).\n    if (childNode) {\n      childNode.removeAttribute('title');\n    }\n    enterTimer.clear();\n    leaveTimer.clear();\n    if (enterDelay || hystersisOpen && enterNextDelay) {\n      enterTimer.start(hystersisOpen ? enterNextDelay : enterDelay, () => {\n        handleOpen(event);\n      });\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleMouseLeave = event => {\n    enterTimer.clear();\n    leaveTimer.start(leaveDelay, () => {\n      handleClose(event);\n    });\n  };\n  const [, setChildIsFocusVisible] = React.useState(false);\n  const handleBlur = event => {\n    if (!isFocusVisible(event.target)) {\n      setChildIsFocusVisible(false);\n      handleMouseLeave(event);\n    }\n  };\n  const handleFocus = event => {\n    // Workaround for https://github.com/facebook/react/issues/7769\n    // The autoFocus of React might trigger the event before the componentDidMount.\n    // We need to account for this eventuality.\n    if (!childNode) {\n      setChildNode(event.currentTarget);\n    }\n    if (isFocusVisible(event.target)) {\n      setChildIsFocusVisible(true);\n      handleMouseOver(event);\n    }\n  };\n  const detectTouchStart = event => {\n    ignoreNonTouchEvents.current = true;\n    const childrenProps = children.props;\n    if (childrenProps.onTouchStart) {\n      childrenProps.onTouchStart(event);\n    }\n  };\n  const handleTouchStart = event => {\n    detectTouchStart(event);\n    leaveTimer.clear();\n    closeTimer.clear();\n    stopTouchInteraction();\n    prevUserSelect.current = document.body.style.WebkitUserSelect;\n    // Prevent iOS text selection on long-tap.\n    document.body.style.WebkitUserSelect = 'none';\n    touchTimer.start(enterTouchDelay, () => {\n      document.body.style.WebkitUserSelect = prevUserSelect.current;\n      handleMouseOver(event);\n    });\n  };\n  const handleTouchEnd = event => {\n    if (children.props.onTouchEnd) {\n      children.props.onTouchEnd(event);\n    }\n    stopTouchInteraction();\n    leaveTimer.start(leaveTouchDelay, () => {\n      handleClose(event);\n    });\n  };\n  React.useEffect(() => {\n    if (!open) {\n      return undefined;\n    }\n\n    /**\n     * @param {KeyboardEvent} nativeEvent\n     */\n    function handleKeyDown(nativeEvent) {\n      if (nativeEvent.key === 'Escape') {\n        handleClose(nativeEvent);\n      }\n    }\n    document.addEventListener('keydown', handleKeyDown);\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n    };\n  }, [handleClose, open]);\n  const handleRef = useForkRef(getReactElementRef(children), setChildNode, ref);\n\n  // There is no point in displaying an empty tooltip.\n  // So we exclude all falsy values, except 0, which is valid.\n  if (!title && title !== 0) {\n    open = false;\n  }\n  const popperRef = React.useRef();\n  const handleMouseMove = event => {\n    const childrenProps = children.props;\n    if (childrenProps.onMouseMove) {\n      childrenProps.onMouseMove(event);\n    }\n    cursorPosition = {\n      x: event.clientX,\n      y: event.clientY\n    };\n    if (popperRef.current) {\n      popperRef.current.update();\n    }\n  };\n  const nameOrDescProps = {};\n  const titleIsString = typeof title === 'string';\n  if (describeChild) {\n    nameOrDescProps.title = !open && titleIsString && !disableHoverListener ? title : null;\n    nameOrDescProps['aria-describedby'] = open ? id : null;\n  } else {\n    nameOrDescProps['aria-label'] = titleIsString ? title : null;\n    nameOrDescProps['aria-labelledby'] = open && !titleIsString ? id : null;\n  }\n  const childrenProps = {\n    ...nameOrDescProps,\n    ...other,\n    ...children.props,\n    className: clsx(other.className, children.props.className),\n    onTouchStart: detectTouchStart,\n    ref: handleRef,\n    ...(followCursor ? {\n      onMouseMove: handleMouseMove\n    } : {})\n  };\n  if (process.env.NODE_ENV !== 'production') {\n    childrenProps['data-mui-internal-clone-element'] = true;\n\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler\n    // eslint-disable-next-line react-hooks/rules-of-hooks -- process.env never changes\n    React.useEffect(() => {\n      if (childNode && !childNode.getAttribute('data-mui-internal-clone-element')) {\n        console.error(['MUI: The `children` component of the Tooltip is not forwarding its props correctly.', 'Please make sure that props are spread on the same element that the ref is applied to.'].join('\\n'));\n      }\n    }, [childNode]);\n  }\n  const interactiveWrapperListeners = {};\n  if (!disableTouchListener) {\n    childrenProps.onTouchStart = handleTouchStart;\n    childrenProps.onTouchEnd = handleTouchEnd;\n  }\n  if (!disableHoverListener) {\n    childrenProps.onMouseOver = composeEventHandler(handleMouseOver, childrenProps.onMouseOver);\n    childrenProps.onMouseLeave = composeEventHandler(handleMouseLeave, childrenProps.onMouseLeave);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onMouseOver = handleMouseOver;\n      interactiveWrapperListeners.onMouseLeave = handleMouseLeave;\n    }\n  }\n  if (!disableFocusListener) {\n    childrenProps.onFocus = composeEventHandler(handleFocus, childrenProps.onFocus);\n    childrenProps.onBlur = composeEventHandler(handleBlur, childrenProps.onBlur);\n    if (!disableInteractive) {\n      interactiveWrapperListeners.onFocus = handleFocus;\n      interactiveWrapperListeners.onBlur = handleBlur;\n    }\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    if (children.props.title) {\n      console.error(['MUI: You have provided a `title` prop to the child of <Tooltip />.', `Remove this title prop \\`${children.props.title}\\` or the Tooltip component.`].join('\\n'));\n    }\n  }\n  const ownerState = {\n    ...props,\n    isRtl,\n    arrow,\n    disableInteractive,\n    placement,\n    PopperComponentProp,\n    touch: ignoreNonTouchEvents.current\n  };\n  const resolvedPopperProps = typeof slotProps.popper === 'function' ? slotProps.popper(ownerState) : slotProps.popper;\n  const popperOptions = React.useMemo(() => {\n    let tooltipModifiers = [{\n      name: 'arrow',\n      enabled: Boolean(arrowRef),\n      options: {\n        element: arrowRef,\n        padding: 4\n      }\n    }];\n    if (PopperProps.popperOptions?.modifiers) {\n      tooltipModifiers = tooltipModifiers.concat(PopperProps.popperOptions.modifiers);\n    }\n    if (resolvedPopperProps?.popperOptions?.modifiers) {\n      tooltipModifiers = tooltipModifiers.concat(resolvedPopperProps.popperOptions.modifiers);\n    }\n    return {\n      ...PopperProps.popperOptions,\n      ...resolvedPopperProps?.popperOptions,\n      modifiers: tooltipModifiers\n    };\n  }, [arrowRef, PopperProps.popperOptions, resolvedPopperProps?.popperOptions]);\n  const classes = useUtilityClasses(ownerState);\n  const resolvedTransitionProps = typeof slotProps.transition === 'function' ? slotProps.transition(ownerState) : slotProps.transition;\n  const externalForwardedProps = {\n    slots: {\n      popper: components.Popper,\n      transition: components.Transition ?? TransitionComponentProp,\n      tooltip: components.Tooltip,\n      arrow: components.Arrow,\n      ...slots\n    },\n    slotProps: {\n      arrow: slotProps.arrow ?? componentsProps.arrow,\n      popper: {\n        ...PopperProps,\n        ...(resolvedPopperProps ?? componentsProps.popper)\n      },\n      // resolvedPopperProps can be spread because it's already an object\n      tooltip: slotProps.tooltip ?? componentsProps.tooltip,\n      transition: {\n        ...TransitionProps,\n        ...(resolvedTransitionProps ?? componentsProps.transition)\n      }\n    }\n  };\n  const [PopperSlot, popperSlotProps] = useSlot('popper', {\n    elementType: TooltipPopper,\n    externalForwardedProps,\n    ownerState,\n    className: clsx(classes.popper, PopperProps?.className)\n  });\n  const [TransitionSlot, transitionSlotProps] = useSlot('transition', {\n    elementType: Grow,\n    externalForwardedProps,\n    ownerState\n  });\n  const [TooltipSlot, tooltipSlotProps] = useSlot('tooltip', {\n    elementType: TooltipTooltip,\n    className: classes.tooltip,\n    externalForwardedProps,\n    ownerState\n  });\n  const [ArrowSlot, arrowSlotProps] = useSlot('arrow', {\n    elementType: TooltipArrow,\n    className: classes.arrow,\n    externalForwardedProps,\n    ownerState,\n    ref: setArrowRef\n  });\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/React.cloneElement(children, childrenProps), /*#__PURE__*/_jsx(PopperSlot, {\n      as: PopperComponentProp ?? Popper,\n      placement: placement,\n      anchorEl: followCursor ? {\n        getBoundingClientRect: () => ({\n          top: cursorPosition.y,\n          left: cursorPosition.x,\n          right: cursorPosition.x,\n          bottom: cursorPosition.y,\n          width: 0,\n          height: 0\n        })\n      } : childNode,\n      popperRef: popperRef,\n      open: childNode ? open : false,\n      id: id,\n      transition: true,\n      ...interactiveWrapperListeners,\n      ...popperSlotProps,\n      popperOptions: popperOptions,\n      children: ({\n        TransitionProps: TransitionPropsInner\n      }) => /*#__PURE__*/_jsx(TransitionSlot, {\n        timeout: theme.transitions.duration.shorter,\n        ...TransitionPropsInner,\n        ...transitionSlotProps,\n        children: /*#__PURE__*/_jsxs(TooltipSlot, {\n          ...tooltipSlotProps,\n          children: [title, arrow ? /*#__PURE__*/_jsx(ArrowSlot, {\n            ...arrowSlotProps\n          }) : null]\n        })\n      })\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Tooltip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, adds an arrow to the tooltip.\n   * @default false\n   */\n  arrow: PropTypes.bool,\n  /**\n   * Tooltip reference element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Arrow: PropTypes.elementType,\n    Popper: PropTypes.elementType,\n    Tooltip: PropTypes.elementType,\n    Transition: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    arrow: PropTypes.object,\n    popper: PropTypes.object,\n    tooltip: PropTypes.object,\n    transition: PropTypes.object\n  }),\n  /**\n   * Set to `true` if the `title` acts as an accessible description.\n   * By default the `title` acts as an accessible label for the child.\n   * @default false\n   */\n  describeChild: PropTypes.bool,\n  /**\n   * Do not respond to focus-visible events.\n   * @default false\n   */\n  disableFocusListener: PropTypes.bool,\n  /**\n   * Do not respond to hover events.\n   * @default false\n   */\n  disableHoverListener: PropTypes.bool,\n  /**\n   * Makes a tooltip not interactive, i.e. it will close when the user\n   * hovers over the tooltip before the `leaveDelay` is expired.\n   * @default false\n   */\n  disableInteractive: PropTypes.bool,\n  /**\n   * Do not respond to long press touch events.\n   * @default false\n   */\n  disableTouchListener: PropTypes.bool,\n  /**\n   * The number of milliseconds to wait before showing the tooltip.\n   * This prop won't impact the enter touch delay (`enterTouchDelay`).\n   * @default 100\n   */\n  enterDelay: PropTypes.number,\n  /**\n   * The number of milliseconds to wait before showing the tooltip when one was already recently opened.\n   * @default 0\n   */\n  enterNextDelay: PropTypes.number,\n  /**\n   * The number of milliseconds a user must touch the element before showing the tooltip.\n   * @default 700\n   */\n  enterTouchDelay: PropTypes.number,\n  /**\n   * If `true`, the tooltip follow the cursor over the wrapped element.\n   * @default false\n   */\n  followCursor: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide this prop. It falls back to a randomly generated id.\n   */\n  id: PropTypes.string,\n  /**\n   * The number of milliseconds to wait before hiding the tooltip.\n   * This prop won't impact the leave touch delay (`leaveTouchDelay`).\n   * @default 0\n   */\n  leaveDelay: PropTypes.number,\n  /**\n   * The number of milliseconds after the user stops touching an element before hiding the tooltip.\n   * @default 1500\n   */\n  leaveTouchDelay: PropTypes.number,\n  /**\n   * Callback fired when the component requests to be closed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be open.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * Tooltip placement.\n   * @default 'bottom'\n   */\n  placement: PropTypes.oneOf(['auto-end', 'auto-start', 'auto', 'bottom-end', 'bottom-start', 'bottom', 'left-end', 'left-start', 'left', 'right-end', 'right-start', 'right', 'top-end', 'top-start', 'top']),\n  /**\n   * The component used for the popper.\n   * @deprecated use the `slots.popper` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Popper`](https://mui.com/material-ui/api/popper/) element.\n   * @deprecated use the `slotProps.popper` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  PopperProps: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    arrow: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popper: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    tooltip: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    transition: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    arrow: PropTypes.elementType,\n    popper: PropTypes.elementType,\n    tooltip: PropTypes.elementType,\n    transition: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Tooltip title. Zero-length titles string, undefined, null and false are never displayed.\n   */\n  title: PropTypes.node,\n  /**\n   * The component used for the transition.\n   * [Follow this guide](https://mui.com/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @deprecated use the `slots.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * Props applied to the transition element.\n   * By default, the element is based on this [`Transition`](https://reactcommunity.org/react-transition-group/transition/) component.\n   * @deprecated use the `slotProps.transition` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  TransitionProps: PropTypes.object\n} : void 0;\nexport default Tooltip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,UAAU,IAAIC,OAAO,QAAQ,uBAAuB;AAC3D,OAAOC,mBAAmB,MAAM,gCAAgC;AAChE,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,SAASC,MAAM,EAAEC,QAAQ,QAAQ,yBAAyB;AAC1D,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,aAAa,MAAM,2BAA2B;AACrD,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,cAAc,IAAIC,sBAAsB,QAAQ,qBAAqB;AAC5E,SAASC,GAAG,IAAIC,IAAI,EAAEC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AAC9D,SAASC,KAAKA,CAACC,KAAK,EAAE;EACpB,OAAOC,IAAI,CAACF,KAAK,CAACC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;AACtC;AACA,MAAME,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,kBAAkB;IAClBC,KAAK;IACLC,KAAK;IACLC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAACL,kBAAkB,IAAI,mBAAmB,EAAEC,KAAK,IAAI,aAAa,CAAC;IACtFK,OAAO,EAAE,CAAC,SAAS,EAAEL,KAAK,IAAI,cAAc,EAAEC,KAAK,IAAI,OAAO,EAAE,mBAAmBtB,UAAU,CAACuB,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzHN,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAO9B,cAAc,CAACiC,KAAK,EAAEf,sBAAsB,EAAEU,OAAO,CAAC;AAC/D,CAAC;AACD,MAAMS,aAAa,GAAGhC,MAAM,CAACM,MAAM,EAAE;EACnC2B,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJf;IACF,CAAC,GAAGc,KAAK;IACT,OAAO,CAACC,MAAM,CAACR,MAAM,EAAE,CAACP,UAAU,CAACE,kBAAkB,IAAIa,MAAM,CAACC,iBAAiB,EAAEhB,UAAU,CAACG,KAAK,IAAIY,MAAM,CAACE,WAAW,EAAE,CAACjB,UAAU,CAACkB,IAAI,IAAIH,MAAM,CAACI,WAAW,CAAC;EACpK;AACF,CAAC,CAAC,CAACvC,SAAS,CAAC,CAAC;EACZwC;AACF,CAAC,MAAM;EACLC,MAAM,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEC,MAAM,CAACb,OAAO;EAC5Ce,aAAa,EAAE,MAAM;EACrBC,QAAQ,EAAE,CAAC;IACTV,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAK,CAACA,UAAU,CAACE,kBAAkB;IACpCuB,KAAK,EAAE;MACLF,aAAa,EAAE;IACjB;EACF,CAAC,EAAE;IACDT,KAAK,EAAEA,CAAC;MACNI;IACF,CAAC,KAAK,CAACA,IAAI;IACXO,KAAK,EAAE;MACLF,aAAa,EAAE;IACjB;EACF,CAAC,EAAE;IACDT,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACG,KAAK;IACtBsB,KAAK,EAAE;MACL,CAAC,uCAAuCnC,cAAc,CAACa,KAAK,EAAE,GAAG;QAC/DuB,GAAG,EAAE,CAAC;QACNC,SAAS,EAAE,SAAS;QACpB,WAAW,EAAE;UACXC,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAC,oCAAoCtC,cAAc,CAACa,KAAK,EAAE,GAAG;QAC5D0B,MAAM,EAAE,CAAC;QACTC,YAAY,EAAE,SAAS;QACvB,WAAW,EAAE;UACXF,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAC,sCAAsCtC,cAAc,CAACa,KAAK,EAAE,GAAG;QAC9D4B,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE,QAAQ;QACf,WAAW,EAAE;UACXJ,eAAe,EAAE;QACnB;MACF,CAAC;MACD,CAAC,qCAAqCtC,cAAc,CAACa,KAAK,EAAE,GAAG;QAC7D4B,MAAM,EAAE,KAAK;QACbC,KAAK,EAAE,QAAQ;QACf,WAAW,EAAE;UACXJ,eAAe,EAAE;QACnB;MACF;IACF;EACF,CAAC,EAAE;IACDd,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACG,KAAK,IAAI,CAACH,UAAU,CAACiC,KAAK;IAC3CR,KAAK,EAAE;MACL,CAAC,sCAAsCnC,cAAc,CAACa,KAAK,EAAE,GAAG;QAC9D+B,IAAI,EAAE,CAAC;QACPC,UAAU,EAAE;MACd;IACF;EACF,CAAC,EAAE;IACDrB,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACG,KAAK,IAAI,CAAC,CAACH,UAAU,CAACiC,KAAK;IAC5CR,KAAK,EAAE;MACL,CAAC,sCAAsCnC,cAAc,CAACa,KAAK,EAAE,GAAG;QAC9DiC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE;MACf;IACF;EACF,CAAC,EAAE;IACDvB,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACG,KAAK,IAAI,CAACH,UAAU,CAACiC,KAAK;IAC3CR,KAAK,EAAE;MACL,CAAC,qCAAqCnC,cAAc,CAACa,KAAK,EAAE,GAAG;QAC7DiC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE;MACf;IACF;EACF,CAAC,EAAE;IACDvB,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACG,KAAK,IAAI,CAAC,CAACH,UAAU,CAACiC,KAAK;IAC5CR,KAAK,EAAE;MACL,CAAC,qCAAqCnC,cAAc,CAACa,KAAK,EAAE,GAAG;QAC7D+B,IAAI,EAAE,CAAC;QACPC,UAAU,EAAE;MACd;IACF;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMG,cAAc,GAAG5D,MAAM,CAAC,KAAK,EAAE;EACnCiC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJf;IACF,CAAC,GAAGc,KAAK;IACT,OAAO,CAACC,MAAM,CAACP,OAAO,EAAER,UAAU,CAACI,KAAK,IAAIW,MAAM,CAACX,KAAK,EAAEJ,UAAU,CAACG,KAAK,IAAIY,MAAM,CAACwB,YAAY,EAAExB,MAAM,CAAC,mBAAmBjC,UAAU,CAACkB,UAAU,CAACK,SAAS,CAACI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EACjL;AACF,CAAC,CAAC,CAAC7B,SAAS,CAAC,CAAC;EACZwC;AACF,CAAC,MAAM;EACLoB,eAAe,EAAEpB,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACE,IAAI,CAACmB,OAAO,CAACC,OAAO,CAACC,EAAE,GAAGrE,KAAK,CAAC8C,KAAK,CAACqB,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC;EAClGC,YAAY,EAAE,CAACzB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAE0B,KAAK,CAACD,YAAY;EACtDE,KAAK,EAAE,CAAC3B,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEqB,OAAO,CAACO,MAAM,CAACC,KAAK;EACjDC,UAAU,EAAE9B,KAAK,CAAC+B,UAAU,CAACD,UAAU;EACvCE,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAEjC,KAAK,CAAC+B,UAAU,CAACG,OAAO,CAAC,EAAE,CAAC;EACtCC,QAAQ,EAAE,GAAG;EACbC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE,YAAY;EACtBC,UAAU,EAAEtC,KAAK,CAAC+B,UAAU,CAACQ,gBAAgB;EAC7C,CAAC,IAAIrE,cAAc,CAACiB,MAAM,mCAAmC,GAAG;IAC9DqB,eAAe,EAAE;EACnB,CAAC;EACD,CAAC,IAAItC,cAAc,CAACiB,MAAM,oCAAoC,GAAG;IAC/DqB,eAAe,EAAE;EACnB,CAAC;EACD,CAAC,IAAItC,cAAc,CAACiB,MAAM,kCAAkC,GAAG;IAC7DqB,eAAe,EAAE,eAAe;IAChCE,YAAY,EAAE;EAChB,CAAC;EACD,CAAC,IAAIxC,cAAc,CAACiB,MAAM,qCAAqC,GAAG;IAChEqB,eAAe,EAAE,YAAY;IAC7BD,SAAS,EAAE;EACb,CAAC;EACDH,QAAQ,EAAE,CAAC;IACTV,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACG,KAAK;IACtBsB,KAAK,EAAE;MACLmC,QAAQ,EAAE,UAAU;MACpBJ,MAAM,EAAE;IACV;EACF,CAAC,EAAE;IACD1C,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACI,KAAK;IACtBqB,KAAK,EAAE;MACL2B,OAAO,EAAE,UAAU;MACnBC,QAAQ,EAAEjC,KAAK,CAAC+B,UAAU,CAACG,OAAO,CAAC,EAAE,CAAC;MACtCO,UAAU,EAAE,GAAGjE,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI;MACjC8D,UAAU,EAAEtC,KAAK,CAAC+B,UAAU,CAACW;IAC/B;EACF,CAAC,EAAE;IACDhD,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAK,CAACA,UAAU,CAACiC,KAAK;IACvBR,KAAK,EAAE;MACL,CAAC,IAAInC,cAAc,CAACiB,MAAM,mCAAmC,GAAG;QAC9D8B,WAAW,EAAE;MACf,CAAC;MACD,CAAC,IAAI/C,cAAc,CAACiB,MAAM,oCAAoC,GAAG;QAC/D4B,UAAU,EAAE;MACd;IACF;EACF,CAAC,EAAE;IACDrB,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAK,CAACA,UAAU,CAACiC,KAAK,IAAIjC,UAAU,CAACI,KAAK;IAC3CqB,KAAK,EAAE;MACL,CAAC,IAAInC,cAAc,CAACiB,MAAM,mCAAmC,GAAG;QAC9D8B,WAAW,EAAE;MACf,CAAC;MACD,CAAC,IAAI/C,cAAc,CAACiB,MAAM,oCAAoC,GAAG;QAC/D4B,UAAU,EAAE;MACd;IACF;EACF,CAAC,EAAE;IACDrB,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAK,CAAC,CAACA,UAAU,CAACiC,KAAK;IACxBR,KAAK,EAAE;MACL,CAAC,IAAInC,cAAc,CAACiB,MAAM,mCAAmC,GAAG;QAC9D4B,UAAU,EAAE;MACd,CAAC;MACD,CAAC,IAAI7C,cAAc,CAACiB,MAAM,oCAAoC,GAAG;QAC/D8B,WAAW,EAAE;MACf;IACF;EACF,CAAC,EAAE;IACDvB,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAK,CAAC,CAACA,UAAU,CAACiC,KAAK,IAAIjC,UAAU,CAACI,KAAK;IAC5CqB,KAAK,EAAE;MACL,CAAC,IAAInC,cAAc,CAACiB,MAAM,mCAAmC,GAAG;QAC9D4B,UAAU,EAAE;MACd,CAAC;MACD,CAAC,IAAI7C,cAAc,CAACiB,MAAM,oCAAoC,GAAG;QAC/D8B,WAAW,EAAE;MACf;IACF;EACF,CAAC,EAAE;IACDvB,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACI,KAAK;IACtBqB,KAAK,EAAE;MACL,CAAC,IAAInC,cAAc,CAACiB,MAAM,kCAAkC,GAAG;QAC7DuB,YAAY,EAAE;MAChB;IACF;EACF,CAAC,EAAE;IACDhB,KAAK,EAAEA,CAAC;MACNd;IACF,CAAC,KAAKA,UAAU,CAACI,KAAK;IACtBqB,KAAK,EAAE;MACL,CAAC,IAAInC,cAAc,CAACiB,MAAM,qCAAqC,GAAG;QAChEoB,SAAS,EAAE;MACb;IACF;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMoC,YAAY,GAAGrF,MAAM,CAAC,MAAM,EAAE;EAClCiC,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAChC,SAAS,CAAC,CAAC;EACZwC;AACF,CAAC,MAAM;EACL4C,QAAQ,EAAE,QAAQ;EAClBJ,QAAQ,EAAE,UAAU;EACpB5B,KAAK,EAAE,KAAK;EACZD,MAAM,EAAE,QAAQ,CAAC;EACjBkC,SAAS,EAAE,YAAY;EACvBlB,KAAK,EAAE3B,KAAK,CAACE,IAAI,GAAGF,KAAK,CAACE,IAAI,CAACmB,OAAO,CAACC,OAAO,CAACC,EAAE,GAAGrE,KAAK,CAAC8C,KAAK,CAACqB,OAAO,CAACG,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;EACvF,WAAW,EAAE;IACXsB,OAAO,EAAE,IAAI;IACbV,MAAM,EAAE,MAAM;IACdW,OAAO,EAAE,OAAO;IAChBnC,KAAK,EAAE,MAAM;IACbD,MAAM,EAAE,MAAM;IACdS,eAAe,EAAE,cAAc;IAC/B4B,SAAS,EAAE;EACb;AACF,CAAC,CAAC,CAAC,CAAC;AACJ,IAAIC,aAAa,GAAG,KAAK;AACzB,MAAMC,cAAc,GAAG,IAAInG,OAAO,CAAC,CAAC;AACpC,IAAIoG,cAAc,GAAG;EACnBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AACD,OAAO,SAASC,SAASA,CAAA,EAAG;EAC1BL,aAAa,GAAG,KAAK;EACrBC,cAAc,CAACK,KAAK,CAAC,CAAC;AACxB;AACA,SAASC,mBAAmBA,CAACC,OAAO,EAAEC,YAAY,EAAE;EAClD,OAAO,CAACC,KAAK,EAAE,GAAGC,MAAM,KAAK;IAC3B,IAAIF,YAAY,EAAE;MAChBA,YAAY,CAACC,KAAK,EAAE,GAAGC,MAAM,CAAC;IAChC;IACAH,OAAO,CAACE,KAAK,EAAE,GAAGC,MAAM,CAAC;EAC3B,CAAC;AACH;;AAEA;AACA,MAAMtC,OAAO,GAAG,aAAa3E,KAAK,CAACkH,UAAU,CAAC,SAASvC,OAAOA,CAACwC,OAAO,EAAEC,GAAG,EAAE;EAC3E,MAAMrE,KAAK,GAAGjC,eAAe,CAAC;IAC5BiC,KAAK,EAAEoE,OAAO;IACdvE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJR,KAAK,GAAG,KAAK;IACbiF,QAAQ,EAAEC,YAAY;IACtBpF,OAAO,EAAEqF,WAAW;IACpBC,UAAU,GAAG,CAAC,CAAC;IACfC,eAAe,GAAG,CAAC,CAAC;IACpBC,aAAa,GAAG,KAAK;IACrBC,oBAAoB,GAAG,KAAK;IAC5BC,oBAAoB,GAAG,KAAK;IAC5BzF,kBAAkB,EAAE0F,sBAAsB,GAAG,KAAK;IAClDC,oBAAoB,GAAG,KAAK;IAC5BC,UAAU,GAAG,GAAG;IAChBC,cAAc,GAAG,CAAC;IAClBC,eAAe,GAAG,GAAG;IACrBC,YAAY,GAAG,KAAK;IACpBC,EAAE,EAAEC,MAAM;IACVC,UAAU,GAAG,CAAC;IACdC,eAAe,GAAG,IAAI;IACtBC,OAAO;IACPC,MAAM;IACNrF,IAAI,EAAEsF,QAAQ;IACdnG,SAAS,GAAG,QAAQ;IACpBoG,eAAe,EAAEC,mBAAmB;IACpCC,WAAW,GAAG,CAAC,CAAC;IAChBC,SAAS,GAAG,CAAC,CAAC;IACdtG,KAAK,GAAG,CAAC,CAAC;IACVuG,KAAK;IACLC,mBAAmB,EAAEC,uBAAuB;IAC5CC,eAAe;IACf,GAAGC;EACL,CAAC,GAAGnG,KAAK;;EAET;EACA,MAAMsE,QAAQ,GAAG,aAAarH,KAAK,CAACmJ,cAAc,CAAC7B,YAAY,CAAC,GAAGA,YAAY,GAAG,aAAa5F,IAAI,CAAC,MAAM,EAAE;IAC1G2F,QAAQ,EAAEC;EACZ,CAAC,CAAC;EACF,MAAMjE,KAAK,GAAGzC,QAAQ,CAAC,CAAC;EACxB,MAAMsD,KAAK,GAAG1D,MAAM,CAAC,CAAC;EACtB,MAAM,CAAC4I,SAAS,EAAEC,YAAY,CAAC,GAAGrJ,KAAK,CAACsJ,QAAQ,CAAC,CAAC;EAClD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxJ,KAAK,CAACsJ,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAMG,oBAAoB,GAAGzJ,KAAK,CAAC0J,MAAM,CAAC,KAAK,CAAC;EAChD,MAAMvH,kBAAkB,GAAG0F,sBAAsB,IAAIK,YAAY;EACjE,MAAMyB,UAAU,GAAGxJ,UAAU,CAAC,CAAC;EAC/B,MAAMyJ,UAAU,GAAGzJ,UAAU,CAAC,CAAC;EAC/B,MAAM0J,UAAU,GAAG1J,UAAU,CAAC,CAAC;EAC/B,MAAM2J,UAAU,GAAG3J,UAAU,CAAC,CAAC;EAC/B,MAAM,CAAC4J,SAAS,EAAEC,YAAY,CAAC,GAAG3I,aAAa,CAAC;IAC9C4I,UAAU,EAAExB,QAAQ;IACpByB,OAAO,EAAE,KAAK;IACdtH,IAAI,EAAE,SAAS;IACfuH,KAAK,EAAE;EACT,CAAC,CAAC;EACF,IAAIhH,IAAI,GAAG4G,SAAS;EACpB,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA;IACA,MAAM;MACJC,OAAO,EAAEC;IACX,CAAC,GAAGxK,KAAK,CAAC0J,MAAM,CAACjB,QAAQ,KAAKgC,SAAS,CAAC;;IAExC;IACA;IACAzK,KAAK,CAAC0K,SAAS,CAAC,MAAM;MACpB,IAAItB,SAAS,IAAIA,SAAS,CAACuB,QAAQ,IAAI,CAACH,YAAY,IAAI1B,KAAK,KAAK,EAAE,IAAIM,SAAS,CAACwB,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;QACpHC,OAAO,CAACC,IAAI,CAAC,CAAC,4EAA4E,EAAE,0CAA0C,EAAE,6EAA6E,EAAE,EAAE,EAAE,iDAAiD,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAC3R;IACF,CAAC,EAAE,CAAClC,KAAK,EAAEM,SAAS,EAAEoB,YAAY,CAAC,CAAC;EACtC;EACA,MAAMrC,EAAE,GAAG/G,KAAK,CAACgH,MAAM,CAAC;EACxB,MAAM6C,cAAc,GAAGjL,KAAK,CAAC0J,MAAM,CAAC,CAAC;EACrC,MAAMwB,oBAAoB,GAAGhK,gBAAgB,CAAC,MAAM;IAClD,IAAI+J,cAAc,CAACV,OAAO,KAAKE,SAAS,EAAE;MACxCU,QAAQ,CAACC,IAAI,CAAC1H,KAAK,CAAC2H,gBAAgB,GAAGJ,cAAc,CAACV,OAAO;MAC7DU,cAAc,CAACV,OAAO,GAAGE,SAAS;IACpC;IACAX,UAAU,CAAClD,KAAK,CAAC,CAAC;EACpB,CAAC,CAAC;EACF5G,KAAK,CAAC0K,SAAS,CAAC,MAAMQ,oBAAoB,EAAE,CAACA,oBAAoB,CAAC,CAAC;EACnE,MAAMI,UAAU,GAAGtE,KAAK,IAAI;IAC1BT,cAAc,CAACK,KAAK,CAAC,CAAC;IACtBN,aAAa,GAAG,IAAI;;IAEpB;IACA;IACA;IACA0D,YAAY,CAAC,IAAI,CAAC;IAClB,IAAIxB,MAAM,IAAI,CAACrF,IAAI,EAAE;MACnBqF,MAAM,CAACxB,KAAK,CAAC;IACf;EACF,CAAC;EACD,MAAMuE,WAAW,GAAGrK,gBAAgB;EACpC;AACF;AACA;EACE8F,KAAK,IAAI;IACPT,cAAc,CAACiF,KAAK,CAAC,GAAG,GAAGnD,UAAU,EAAE,MAAM;MAC3C/B,aAAa,GAAG,KAAK;IACvB,CAAC,CAAC;IACF0D,YAAY,CAAC,KAAK,CAAC;IACnB,IAAIzB,OAAO,IAAIpF,IAAI,EAAE;MACnBoF,OAAO,CAACvB,KAAK,CAAC;IAChB;IACA2C,UAAU,CAAC6B,KAAK,CAACnI,KAAK,CAACoI,WAAW,CAACC,QAAQ,CAACC,QAAQ,EAAE,MAAM;MAC1DlC,oBAAoB,CAACc,OAAO,GAAG,KAAK;IACtC,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,MAAMqB,eAAe,GAAG5E,KAAK,IAAI;IAC/B,IAAIyC,oBAAoB,CAACc,OAAO,IAAIvD,KAAK,CAAC6E,IAAI,KAAK,YAAY,EAAE;MAC/D;IACF;;IAEA;IACA;IACA;IACA,IAAIzC,SAAS,EAAE;MACbA,SAAS,CAAC0C,eAAe,CAAC,OAAO,CAAC;IACpC;IACAlC,UAAU,CAAChD,KAAK,CAAC,CAAC;IAClBiD,UAAU,CAACjD,KAAK,CAAC,CAAC;IAClB,IAAImB,UAAU,IAAIzB,aAAa,IAAI0B,cAAc,EAAE;MACjD4B,UAAU,CAAC4B,KAAK,CAAClF,aAAa,GAAG0B,cAAc,GAAGD,UAAU,EAAE,MAAM;QAClEuD,UAAU,CAACtE,KAAK,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLsE,UAAU,CAACtE,KAAK,CAAC;IACnB;EACF,CAAC;EACD,MAAM+E,gBAAgB,GAAG/E,KAAK,IAAI;IAChC4C,UAAU,CAAChD,KAAK,CAAC,CAAC;IAClBiD,UAAU,CAAC2B,KAAK,CAACnD,UAAU,EAAE,MAAM;MACjCkD,WAAW,CAACvE,KAAK,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM,GAAGgF,sBAAsB,CAAC,GAAGhM,KAAK,CAACsJ,QAAQ,CAAC,KAAK,CAAC;EACxD,MAAM2C,UAAU,GAAGjF,KAAK,IAAI;IAC1B,IAAI,CAACvG,cAAc,CAACuG,KAAK,CAACkF,MAAM,CAAC,EAAE;MACjCF,sBAAsB,CAAC,KAAK,CAAC;MAC7BD,gBAAgB,CAAC/E,KAAK,CAAC;IACzB;EACF,CAAC;EACD,MAAMmF,WAAW,GAAGnF,KAAK,IAAI;IAC3B;IACA;IACA;IACA,IAAI,CAACoC,SAAS,EAAE;MACdC,YAAY,CAACrC,KAAK,CAACoF,aAAa,CAAC;IACnC;IACA,IAAI3L,cAAc,CAACuG,KAAK,CAACkF,MAAM,CAAC,EAAE;MAChCF,sBAAsB,CAAC,IAAI,CAAC;MAC5BJ,eAAe,CAAC5E,KAAK,CAAC;IACxB;EACF,CAAC;EACD,MAAMqF,gBAAgB,GAAGrF,KAAK,IAAI;IAChCyC,oBAAoB,CAACc,OAAO,GAAG,IAAI;IACnC,MAAM+B,aAAa,GAAGjF,QAAQ,CAACtE,KAAK;IACpC,IAAIuJ,aAAa,CAACC,YAAY,EAAE;MAC9BD,aAAa,CAACC,YAAY,CAACvF,KAAK,CAAC;IACnC;EACF,CAAC;EACD,MAAMwF,gBAAgB,GAAGxF,KAAK,IAAI;IAChCqF,gBAAgB,CAACrF,KAAK,CAAC;IACvB6C,UAAU,CAACjD,KAAK,CAAC,CAAC;IAClB+C,UAAU,CAAC/C,KAAK,CAAC,CAAC;IAClBsE,oBAAoB,CAAC,CAAC;IACtBD,cAAc,CAACV,OAAO,GAAGY,QAAQ,CAACC,IAAI,CAAC1H,KAAK,CAAC2H,gBAAgB;IAC7D;IACAF,QAAQ,CAACC,IAAI,CAAC1H,KAAK,CAAC2H,gBAAgB,GAAG,MAAM;IAC7CvB,UAAU,CAAC0B,KAAK,CAACvD,eAAe,EAAE,MAAM;MACtCkD,QAAQ,CAACC,IAAI,CAAC1H,KAAK,CAAC2H,gBAAgB,GAAGJ,cAAc,CAACV,OAAO;MAC7DqB,eAAe,CAAC5E,KAAK,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EACD,MAAMyF,cAAc,GAAGzF,KAAK,IAAI;IAC9B,IAAIK,QAAQ,CAACtE,KAAK,CAAC2J,UAAU,EAAE;MAC7BrF,QAAQ,CAACtE,KAAK,CAAC2J,UAAU,CAAC1F,KAAK,CAAC;IAClC;IACAkE,oBAAoB,CAAC,CAAC;IACtBrB,UAAU,CAAC2B,KAAK,CAAClD,eAAe,EAAE,MAAM;MACtCiD,WAAW,CAACvE,KAAK,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;EACDhH,KAAK,CAAC0K,SAAS,CAAC,MAAM;IACpB,IAAI,CAACvH,IAAI,EAAE;MACT,OAAOsH,SAAS;IAClB;;IAEA;AACJ;AACA;IACI,SAASkC,aAAaA,CAACC,WAAW,EAAE;MAClC,IAAIA,WAAW,CAACC,GAAG,KAAK,QAAQ,EAAE;QAChCtB,WAAW,CAACqB,WAAW,CAAC;MAC1B;IACF;IACAzB,QAAQ,CAAC2B,gBAAgB,CAAC,SAAS,EAAEH,aAAa,CAAC;IACnD,OAAO,MAAM;MACXxB,QAAQ,CAAC4B,mBAAmB,CAAC,SAAS,EAAEJ,aAAa,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAACpB,WAAW,EAAEpI,IAAI,CAAC,CAAC;EACvB,MAAM6J,SAAS,GAAG7L,UAAU,CAACT,kBAAkB,CAAC2G,QAAQ,CAAC,EAAEgC,YAAY,EAAEjC,GAAG,CAAC;;EAE7E;EACA;EACA,IAAI,CAAC0B,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE;IACzB3F,IAAI,GAAG,KAAK;EACd;EACA,MAAM8J,SAAS,GAAGjN,KAAK,CAAC0J,MAAM,CAAC,CAAC;EAChC,MAAMwD,eAAe,GAAGlG,KAAK,IAAI;IAC/B,MAAMsF,aAAa,GAAGjF,QAAQ,CAACtE,KAAK;IACpC,IAAIuJ,aAAa,CAACa,WAAW,EAAE;MAC7Bb,aAAa,CAACa,WAAW,CAACnG,KAAK,CAAC;IAClC;IACAR,cAAc,GAAG;MACfC,CAAC,EAAEO,KAAK,CAACoG,OAAO;MAChB1G,CAAC,EAAEM,KAAK,CAACqG;IACX,CAAC;IACD,IAAIJ,SAAS,CAAC1C,OAAO,EAAE;MACrB0C,SAAS,CAAC1C,OAAO,CAAC+C,MAAM,CAAC,CAAC;IAC5B;EACF,CAAC;EACD,MAAMC,eAAe,GAAG,CAAC,CAAC;EAC1B,MAAMC,aAAa,GAAG,OAAO1E,KAAK,KAAK,QAAQ;EAC/C,IAAIpB,aAAa,EAAE;IACjB6F,eAAe,CAACzE,KAAK,GAAG,CAAC3F,IAAI,IAAIqK,aAAa,IAAI,CAAC5F,oBAAoB,GAAGkB,KAAK,GAAG,IAAI;IACtFyE,eAAe,CAAC,kBAAkB,CAAC,GAAGpK,IAAI,GAAGgF,EAAE,GAAG,IAAI;EACxD,CAAC,MAAM;IACLoF,eAAe,CAAC,YAAY,CAAC,GAAGC,aAAa,GAAG1E,KAAK,GAAG,IAAI;IAC5DyE,eAAe,CAAC,iBAAiB,CAAC,GAAGpK,IAAI,IAAI,CAACqK,aAAa,GAAGrF,EAAE,GAAG,IAAI;EACzE;EACA,MAAMmE,aAAa,GAAG;IACpB,GAAGiB,eAAe;IAClB,GAAGrE,KAAK;IACR,GAAG7B,QAAQ,CAACtE,KAAK;IACjB0K,SAAS,EAAEvN,IAAI,CAACgJ,KAAK,CAACuE,SAAS,EAAEpG,QAAQ,CAACtE,KAAK,CAAC0K,SAAS,CAAC;IAC1DlB,YAAY,EAAEF,gBAAgB;IAC9BjF,GAAG,EAAE4F,SAAS;IACd,IAAI9E,YAAY,GAAG;MACjBiF,WAAW,EAAED;IACf,CAAC,GAAG,CAAC,CAAC;EACR,CAAC;EACD,IAAI9C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCgC,aAAa,CAAC,iCAAiC,CAAC,GAAG,IAAI;;IAEvD;IACA;IACAtM,KAAK,CAAC0K,SAAS,CAAC,MAAM;MACpB,IAAItB,SAAS,IAAI,CAACA,SAAS,CAACsE,YAAY,CAAC,iCAAiC,CAAC,EAAE;QAC3E5C,OAAO,CAAC6C,KAAK,CAAC,CAAC,qFAAqF,EAAE,wFAAwF,CAAC,CAAC3C,IAAI,CAAC,IAAI,CAAC,CAAC;MAC7M;IACF,CAAC,EAAE,CAAC5B,SAAS,CAAC,CAAC;EACjB;EACA,MAAMwE,2BAA2B,GAAG,CAAC,CAAC;EACtC,IAAI,CAAC9F,oBAAoB,EAAE;IACzBwE,aAAa,CAACC,YAAY,GAAGC,gBAAgB;IAC7CF,aAAa,CAACI,UAAU,GAAGD,cAAc;EAC3C;EACA,IAAI,CAAC7E,oBAAoB,EAAE;IACzB0E,aAAa,CAACuB,WAAW,GAAGhH,mBAAmB,CAAC+E,eAAe,EAAEU,aAAa,CAACuB,WAAW,CAAC;IAC3FvB,aAAa,CAACwB,YAAY,GAAGjH,mBAAmB,CAACkF,gBAAgB,EAAEO,aAAa,CAACwB,YAAY,CAAC;IAC9F,IAAI,CAAC3L,kBAAkB,EAAE;MACvByL,2BAA2B,CAACC,WAAW,GAAGjC,eAAe;MACzDgC,2BAA2B,CAACE,YAAY,GAAG/B,gBAAgB;IAC7D;EACF;EACA,IAAI,CAACpE,oBAAoB,EAAE;IACzB2E,aAAa,CAACyB,OAAO,GAAGlH,mBAAmB,CAACsF,WAAW,EAAEG,aAAa,CAACyB,OAAO,CAAC;IAC/EzB,aAAa,CAAC0B,MAAM,GAAGnH,mBAAmB,CAACoF,UAAU,EAAEK,aAAa,CAAC0B,MAAM,CAAC;IAC5E,IAAI,CAAC7L,kBAAkB,EAAE;MACvByL,2BAA2B,CAACG,OAAO,GAAG5B,WAAW;MACjDyB,2BAA2B,CAACI,MAAM,GAAG/B,UAAU;IACjD;EACF;EACA,IAAI7B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIjD,QAAQ,CAACtE,KAAK,CAAC+F,KAAK,EAAE;MACxBgC,OAAO,CAAC6C,KAAK,CAAC,CAAC,oEAAoE,EAAE,4BAA4BtG,QAAQ,CAACtE,KAAK,CAAC+F,KAAK,8BAA8B,CAAC,CAACkC,IAAI,CAAC,IAAI,CAAC,CAAC;IAClL;EACF;EACA,MAAM/I,UAAU,GAAG;IACjB,GAAGc,KAAK;IACRmB,KAAK;IACL9B,KAAK;IACLD,kBAAkB;IAClBG,SAAS;IACTqG,mBAAmB;IACnBtG,KAAK,EAAEoH,oBAAoB,CAACc;EAC9B,CAAC;EACD,MAAM0D,mBAAmB,GAAG,OAAOpF,SAAS,CAACrG,MAAM,KAAK,UAAU,GAAGqG,SAAS,CAACrG,MAAM,CAACP,UAAU,CAAC,GAAG4G,SAAS,CAACrG,MAAM;EACpH,MAAM0L,aAAa,GAAGlO,KAAK,CAACmO,OAAO,CAAC,MAAM;IACxC,IAAIC,gBAAgB,GAAG,CAAC;MACtBxL,IAAI,EAAE,OAAO;MACbyL,OAAO,EAAEC,OAAO,CAAC/E,QAAQ,CAAC;MAC1BgF,OAAO,EAAE;QACPC,OAAO,EAAEjF,QAAQ;QACjBlE,OAAO,EAAE;MACX;IACF,CAAC,CAAC;IACF,IAAIuD,WAAW,CAACsF,aAAa,EAAEO,SAAS,EAAE;MACxCL,gBAAgB,GAAGA,gBAAgB,CAACM,MAAM,CAAC9F,WAAW,CAACsF,aAAa,CAACO,SAAS,CAAC;IACjF;IACA,IAAIR,mBAAmB,EAAEC,aAAa,EAAEO,SAAS,EAAE;MACjDL,gBAAgB,GAAGA,gBAAgB,CAACM,MAAM,CAACT,mBAAmB,CAACC,aAAa,CAACO,SAAS,CAAC;IACzF;IACA,OAAO;MACL,GAAG7F,WAAW,CAACsF,aAAa;MAC5B,GAAGD,mBAAmB,EAAEC,aAAa;MACrCO,SAAS,EAAEL;IACb,CAAC;EACH,CAAC,EAAE,CAAC7E,QAAQ,EAAEX,WAAW,CAACsF,aAAa,EAAED,mBAAmB,EAAEC,aAAa,CAAC,CAAC;EAC7E,MAAMhM,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM0M,uBAAuB,GAAG,OAAO9F,SAAS,CAAC+F,UAAU,KAAK,UAAU,GAAG/F,SAAS,CAAC+F,UAAU,CAAC3M,UAAU,CAAC,GAAG4G,SAAS,CAAC+F,UAAU;EACpI,MAAMC,sBAAsB,GAAG;IAC7BtM,KAAK,EAAE;MACLC,MAAM,EAAEgF,UAAU,CAACvG,MAAM;MACzB2N,UAAU,EAAEpH,UAAU,CAACsH,UAAU,IAAI9F,uBAAuB;MAC5DvG,OAAO,EAAE+E,UAAU,CAAC7C,OAAO;MAC3BvC,KAAK,EAAEoF,UAAU,CAACuH,KAAK;MACvB,GAAGxM;IACL,CAAC;IACDsG,SAAS,EAAE;MACTzG,KAAK,EAAEyG,SAAS,CAACzG,KAAK,IAAIqF,eAAe,CAACrF,KAAK;MAC/CI,MAAM,EAAE;QACN,GAAGoG,WAAW;QACd,IAAIqF,mBAAmB,IAAIxG,eAAe,CAACjF,MAAM;MACnD,CAAC;MACD;MACAC,OAAO,EAAEoG,SAAS,CAACpG,OAAO,IAAIgF,eAAe,CAAChF,OAAO;MACrDmM,UAAU,EAAE;QACV,GAAG3F,eAAe;QAClB,IAAI0F,uBAAuB,IAAIlH,eAAe,CAACmH,UAAU;MAC3D;IACF;EACF,CAAC;EACD,MAAM,CAACI,UAAU,EAAEC,eAAe,CAAC,GAAG3N,OAAO,CAAC,QAAQ,EAAE;IACtD4N,WAAW,EAAEvM,aAAa;IAC1BkM,sBAAsB;IACtB5M,UAAU;IACVwL,SAAS,EAAEvN,IAAI,CAACgC,OAAO,CAACM,MAAM,EAAEoG,WAAW,EAAE6E,SAAS;EACxD,CAAC,CAAC;EACF,MAAM,CAAC0B,cAAc,EAAEC,mBAAmB,CAAC,GAAG9N,OAAO,CAAC,YAAY,EAAE;IAClE4N,WAAW,EAAElO,IAAI;IACjB6N,sBAAsB;IACtB5M;EACF,CAAC,CAAC;EACF,MAAM,CAACoN,WAAW,EAAEC,gBAAgB,CAAC,GAAGhO,OAAO,CAAC,SAAS,EAAE;IACzD4N,WAAW,EAAE3K,cAAc;IAC3BkJ,SAAS,EAAEvL,OAAO,CAACO,OAAO;IAC1BoM,sBAAsB;IACtB5M;EACF,CAAC,CAAC;EACF,MAAM,CAACsN,SAAS,EAAEC,cAAc,CAAC,GAAGlO,OAAO,CAAC,OAAO,EAAE;IACnD4N,WAAW,EAAElJ,YAAY;IACzByH,SAAS,EAAEvL,OAAO,CAACE,KAAK;IACxByM,sBAAsB;IACtB5M,UAAU;IACVmF,GAAG,EAAEoC;EACP,CAAC,CAAC;EACF,OAAO,aAAa5H,KAAK,CAAC5B,KAAK,CAACyP,QAAQ,EAAE;IACxCpI,QAAQ,EAAE,CAAC,aAAarH,KAAK,CAAC0P,YAAY,CAACrI,QAAQ,EAAEiF,aAAa,CAAC,EAAE,aAAa5K,IAAI,CAACsN,UAAU,EAAE;MACjGW,EAAE,EAAEhH,mBAAmB,IAAI1H,MAAM;MACjCqB,SAAS,EAAEA,SAAS;MACpBsN,QAAQ,EAAE1H,YAAY,GAAG;QACvB2H,qBAAqB,EAAEA,CAAA,MAAO;UAC5BlM,GAAG,EAAE6C,cAAc,CAACE,CAAC;UACrBvC,IAAI,EAAEqC,cAAc,CAACC,CAAC;UACtBpC,KAAK,EAAEmC,cAAc,CAACC,CAAC;UACvB3C,MAAM,EAAE0C,cAAc,CAACE,CAAC;UACxBzC,KAAK,EAAE,CAAC;UACRD,MAAM,EAAE;QACV,CAAC;MACH,CAAC,GAAGoF,SAAS;MACb6D,SAAS,EAAEA,SAAS;MACpB9J,IAAI,EAAEiG,SAAS,GAAGjG,IAAI,GAAG,KAAK;MAC9BgF,EAAE,EAAEA,EAAE;MACNyG,UAAU,EAAE,IAAI;MAChB,GAAGhB,2BAA2B;MAC9B,GAAGqB,eAAe;MAClBf,aAAa,EAAEA,aAAa;MAC5B7G,QAAQ,EAAEA,CAAC;QACT4B,eAAe,EAAE6G;MACnB,CAAC,KAAK,aAAapO,IAAI,CAACyN,cAAc,EAAE;QACtCY,OAAO,EAAE1M,KAAK,CAACoI,WAAW,CAACC,QAAQ,CAACsE,OAAO;QAC3C,GAAGF,oBAAoB;QACvB,GAAGV,mBAAmB;QACtB/H,QAAQ,EAAE,aAAazF,KAAK,CAACyN,WAAW,EAAE;UACxC,GAAGC,gBAAgB;UACnBjI,QAAQ,EAAE,CAACyB,KAAK,EAAE1G,KAAK,GAAG,aAAaV,IAAI,CAAC6N,SAAS,EAAE;YACrD,GAAGC;UACL,CAAC,CAAC,GAAG,IAAI;QACX,CAAC;MACH,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFpF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG3F,OAAO,CAACsL,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACE7N,KAAK,EAAEnC,SAAS,CAACiQ,IAAI;EACrB;AACF;AACA;EACE7I,QAAQ,EAAEhH,mBAAmB,CAAC8P,UAAU;EACxC;AACF;AACA;EACEjO,OAAO,EAAEjC,SAAS,CAACmQ,MAAM;EACzB;AACF;AACA;EACE3C,SAAS,EAAExN,SAAS,CAACoQ,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;EACE7I,UAAU,EAAEvH,SAAS,CAAC8E,KAAK,CAAC;IAC1BgK,KAAK,EAAE9O,SAAS,CAACiP,WAAW;IAC5BjO,MAAM,EAAEhB,SAAS,CAACiP,WAAW;IAC7BvK,OAAO,EAAE1E,SAAS,CAACiP,WAAW;IAC9BJ,UAAU,EAAE7O,SAAS,CAACiP;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEzH,eAAe,EAAExH,SAAS,CAAC8E,KAAK,CAAC;IAC/B3C,KAAK,EAAEnC,SAAS,CAACmQ,MAAM;IACvB5N,MAAM,EAAEvC,SAAS,CAACmQ,MAAM;IACxB3N,OAAO,EAAExC,SAAS,CAACmQ,MAAM;IACzBxB,UAAU,EAAE3O,SAAS,CAACmQ;EACxB,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE1I,aAAa,EAAEzH,SAAS,CAACiQ,IAAI;EAC7B;AACF;AACA;AACA;EACEvI,oBAAoB,EAAE1H,SAAS,CAACiQ,IAAI;EACpC;AACF;AACA;AACA;EACEtI,oBAAoB,EAAE3H,SAAS,CAACiQ,IAAI;EACpC;AACF;AACA;AACA;AACA;EACE/N,kBAAkB,EAAElC,SAAS,CAACiQ,IAAI;EAClC;AACF;AACA;AACA;EACEpI,oBAAoB,EAAE7H,SAAS,CAACiQ,IAAI;EACpC;AACF;AACA;AACA;AACA;EACEnI,UAAU,EAAE9H,SAAS,CAACqQ,MAAM;EAC5B;AACF;AACA;AACA;EACEtI,cAAc,EAAE/H,SAAS,CAACqQ,MAAM;EAChC;AACF;AACA;AACA;EACErI,eAAe,EAAEhI,SAAS,CAACqQ,MAAM;EACjC;AACF;AACA;AACA;EACEpI,YAAY,EAAEjI,SAAS,CAACiQ,IAAI;EAC5B;AACF;AACA;AACA;EACE/H,EAAE,EAAElI,SAAS,CAACoQ,MAAM;EACpB;AACF;AACA;AACA;AACA;EACEhI,UAAU,EAAEpI,SAAS,CAACqQ,MAAM;EAC5B;AACF;AACA;AACA;EACEhI,eAAe,EAAErI,SAAS,CAACqQ,MAAM;EACjC;AACF;AACA;AACA;AACA;EACE/H,OAAO,EAAEtI,SAAS,CAACsQ,IAAI;EACvB;AACF;AACA;AACA;AACA;EACE/H,MAAM,EAAEvI,SAAS,CAACsQ,IAAI;EACtB;AACF;AACA;EACEpN,IAAI,EAAElD,SAAS,CAACiQ,IAAI;EACpB;AACF;AACA;AACA;EACE5N,SAAS,EAAErC,SAAS,CAACuQ,KAAK,CAAC,CAAC,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;EAC5M;AACF;AACA;AACA;EACE9H,eAAe,EAAEzI,SAAS,CAACiP,WAAW;EACtC;AACF;AACA;AACA;AACA;EACEtG,WAAW,EAAE3I,SAAS,CAACmQ,MAAM;EAC7B;AACF;AACA;AACA;EACEvH,SAAS,EAAE5I,SAAS,CAAC8E,KAAK,CAAC;IACzB3C,KAAK,EAAEnC,SAAS,CAACwQ,SAAS,CAAC,CAACxQ,SAAS,CAACsQ,IAAI,EAAEtQ,SAAS,CAACmQ,MAAM,CAAC,CAAC;IAC9D5N,MAAM,EAAEvC,SAAS,CAACwQ,SAAS,CAAC,CAACxQ,SAAS,CAACsQ,IAAI,EAAEtQ,SAAS,CAACmQ,MAAM,CAAC,CAAC;IAC/D3N,OAAO,EAAExC,SAAS,CAACwQ,SAAS,CAAC,CAACxQ,SAAS,CAACsQ,IAAI,EAAEtQ,SAAS,CAACmQ,MAAM,CAAC,CAAC;IAChExB,UAAU,EAAE3O,SAAS,CAACwQ,SAAS,CAAC,CAACxQ,SAAS,CAACsQ,IAAI,EAAEtQ,SAAS,CAACmQ,MAAM,CAAC;EACpE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE7N,KAAK,EAAEtC,SAAS,CAAC8E,KAAK,CAAC;IACrB3C,KAAK,EAAEnC,SAAS,CAACiP,WAAW;IAC5B1M,MAAM,EAAEvC,SAAS,CAACiP,WAAW;IAC7BzM,OAAO,EAAExC,SAAS,CAACiP,WAAW;IAC9BN,UAAU,EAAE3O,SAAS,CAACiP;EACxB,CAAC,CAAC;EACF;AACF;AACA;EACEwB,EAAE,EAAEzQ,SAAS,CAACwQ,SAAS,CAAC,CAACxQ,SAAS,CAAC0Q,OAAO,CAAC1Q,SAAS,CAACwQ,SAAS,CAAC,CAACxQ,SAAS,CAACsQ,IAAI,EAAEtQ,SAAS,CAACmQ,MAAM,EAAEnQ,SAAS,CAACiQ,IAAI,CAAC,CAAC,CAAC,EAAEjQ,SAAS,CAACsQ,IAAI,EAAEtQ,SAAS,CAACmQ,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEtH,KAAK,EAAE7I,SAAS,CAAC2Q,IAAI;EACrB;AACF;AACA;AACA;AACA;EACE7H,mBAAmB,EAAE9I,SAAS,CAACiP,WAAW;EAC1C;AACF;AACA;AACA;AACA;AACA;EACEjG,eAAe,EAAEhJ,SAAS,CAACmQ;AAC7B,CAAC,GAAG,KAAK,CAAC;AACV,eAAezL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}