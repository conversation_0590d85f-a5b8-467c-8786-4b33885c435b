{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * Merges refs into a single memoized callback ref or `null`.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */\nexport default function useForkRef(...refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return null;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup?.());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}", "map": {"version": 3, "names": ["React", "useForkRef", "refs", "cleanupRef", "useRef", "undefined", "refEffect", "useCallback", "instance", "cleanups", "map", "ref", "refC<PERSON><PERSON>", "refCleanup", "current", "for<PERSON>ach", "useMemo", "every", "value"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/utils/esm/useForkRef/useForkRef.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * Merges refs into a single memoized callback ref or `null`.\n *\n * ```tsx\n * const rootRef = React.useRef<Instance>(null);\n * const refFork = useForkRef(rootRef, props.ref);\n *\n * return (\n *   <Root {...props} ref={refFork} />\n * );\n * ```\n *\n * @param {Array<React.Ref<Instance> | undefined>} refs The ref array.\n * @returns {React.RefCallback<Instance> | null} The new ref callback.\n */\nexport default function useForkRef(...refs) {\n  const cleanupRef = React.useRef(undefined);\n  const refEffect = React.useCallback(instance => {\n    const cleanups = refs.map(ref => {\n      if (ref == null) {\n        return null;\n      }\n      if (typeof ref === 'function') {\n        const refCallback = ref;\n        const refCleanup = refCallback(instance);\n        return typeof refCleanup === 'function' ? refCleanup : () => {\n          refCallback(null);\n        };\n      }\n      ref.current = instance;\n      return () => {\n        ref.current = null;\n      };\n    });\n    return () => {\n      cleanups.forEach(refCleanup => refCleanup?.());\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n  return React.useMemo(() => {\n    if (refs.every(ref => ref == null)) {\n      return null;\n    }\n    return value => {\n      if (cleanupRef.current) {\n        cleanupRef.current();\n        cleanupRef.current = undefined;\n      }\n      if (value != null) {\n        cleanupRef.current = refEffect(value);\n      }\n    };\n    // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- intentionally ignoring that the dependency array must be an array literal\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, refs);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAAC,GAAGC,IAAI,EAAE;EAC1C,MAAMC,UAAU,GAAGH,KAAK,CAACI,MAAM,CAACC,SAAS,CAAC;EAC1C,MAAMC,SAAS,GAAGN,KAAK,CAACO,WAAW,CAACC,QAAQ,IAAI;IAC9C,MAAMC,QAAQ,GAAGP,IAAI,CAACQ,GAAG,CAACC,GAAG,IAAI;MAC/B,IAAIA,GAAG,IAAI,IAAI,EAAE;QACf,OAAO,IAAI;MACb;MACA,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;QAC7B,MAAMC,WAAW,GAAGD,GAAG;QACvB,MAAME,UAAU,GAAGD,WAAW,CAACJ,QAAQ,CAAC;QACxC,OAAO,OAAOK,UAAU,KAAK,UAAU,GAAGA,UAAU,GAAG,MAAM;UAC3DD,WAAW,CAAC,IAAI,CAAC;QACnB,CAAC;MACH;MACAD,GAAG,CAACG,OAAO,GAAGN,QAAQ;MACtB,OAAO,MAAM;QACXG,GAAG,CAACG,OAAO,GAAG,IAAI;MACpB,CAAC;IACH,CAAC,CAAC;IACF,OAAO,MAAM;MACXL,QAAQ,CAACM,OAAO,CAACF,UAAU,IAAIA,UAAU,GAAG,CAAC,CAAC;IAChD,CAAC;IACD;EACF,CAAC,EAAEX,IAAI,CAAC;EACR,OAAOF,KAAK,CAACgB,OAAO,CAAC,MAAM;IACzB,IAAId,IAAI,CAACe,KAAK,CAACN,GAAG,IAAIA,GAAG,IAAI,IAAI,CAAC,EAAE;MAClC,OAAO,IAAI;IACb;IACA,OAAOO,KAAK,IAAI;MACd,IAAIf,UAAU,CAACW,OAAO,EAAE;QACtBX,UAAU,CAACW,OAAO,CAAC,CAAC;QACpBX,UAAU,CAACW,OAAO,GAAGT,SAAS;MAChC;MACA,IAAIa,KAAK,IAAI,IAAI,EAAE;QACjBf,UAAU,CAACW,OAAO,GAAGR,SAAS,CAACY,KAAK,CAAC;MACvC;IACF,CAAC;IACD;IACA;EACF,CAAC,EAAEhB,IAAI,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}