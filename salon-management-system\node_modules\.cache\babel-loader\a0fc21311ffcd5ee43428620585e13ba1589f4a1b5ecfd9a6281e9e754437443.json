{"ast": null, "code": "var weakMemoize = function weakMemoize(func) {\n  var cache = new WeakMap();\n  return function (arg) {\n    if (cache.has(arg)) {\n      // Use non-null assertion because we just checked that the cache `has` it\n      // This allows us to remove `undefined` from the return value\n      return cache.get(arg);\n    }\n    var ret = func(arg);\n    cache.set(arg, ret);\n    return ret;\n  };\n};\nexport { weakMemoize as default };", "map": {"version": 3, "names": ["weakMemoize", "func", "cache", "WeakMap", "arg", "has", "get", "ret", "set", "default"], "sources": ["D:/Project/salon-management-system/node_modules/@emotion/weak-memoize/dist/emotion-weak-memoize.esm.js"], "sourcesContent": ["var weakMemoize = function weakMemoize(func) {\n  var cache = new WeakMap();\n  return function (arg) {\n    if (cache.has(arg)) {\n      // Use non-null assertion because we just checked that the cache `has` it\n      // This allows us to remove `undefined` from the return value\n      return cache.get(arg);\n    }\n\n    var ret = func(arg);\n    cache.set(arg, ret);\n    return ret;\n  };\n};\n\nexport { weakMemoize as default };\n"], "mappings": "AAAA,IAAIA,WAAW,GAAG,SAASA,WAAWA,CAACC,IAAI,EAAE;EAC3C,IAAIC,KAAK,GAAG,IAAIC,OAAO,CAAC,CAAC;EACzB,OAAO,UAAUC,GAAG,EAAE;IACpB,IAAIF,KAAK,CAACG,GAAG,CAACD,GAAG,CAAC,EAAE;MAClB;MACA;MACA,OAAOF,KAAK,CAACI,GAAG,CAACF,GAAG,CAAC;IACvB;IAEA,IAAIG,GAAG,GAAGN,IAAI,CAACG,GAAG,CAAC;IACnBF,KAAK,CAACM,GAAG,CAACJ,GAAG,EAAEG,GAAG,CAAC;IACnB,OAAOA,GAAG;EACZ,CAAC;AACH,CAAC;AAED,SAASP,WAAW,IAAIS,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}