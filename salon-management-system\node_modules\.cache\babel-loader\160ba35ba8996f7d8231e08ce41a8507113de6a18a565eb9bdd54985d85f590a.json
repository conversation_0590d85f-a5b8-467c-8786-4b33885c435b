{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport getOverlayAlpha from \"../styles/getOverlayAlpha.js\";\nimport { getPaperUtilityClass } from \"./paperClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  color: (theme.vars || theme).palette.text.primary,\n  transition: theme.transitions.create('box-shadow'),\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.square,\n    style: {\n      borderRadius: theme.shape.borderRadius\n    }\n  }, {\n    props: {\n      variant: 'outlined'\n    },\n    style: {\n      border: `1px solid ${(theme.vars || theme).palette.divider}`\n    }\n  }, {\n    props: {\n      variant: 'elevation'\n    },\n    style: {\n      boxShadow: 'var(--Paper-shadow)',\n      backgroundImage: 'var(--Paper-overlay)'\n    }\n  }]\n})));\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const theme = useTheme();\n  const {\n    className,\n    component = 'div',\n    elevation = 1,\n    square = false,\n    variant = 'elevation',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    elevation,\n    square,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, {\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    style: {\n      ...(variant === 'elevation' && {\n        '--Paper-shadow': (theme.vars || theme).shadows[elevation],\n        ...(theme.vars && {\n          '--Paper-overlay': theme.vars.overlays?.[elevation]\n        }),\n        ...(!theme.vars && theme.palette.mode === 'dark' && {\n          '--Paper-overlay': `linear-gradient(${alpha('#fff', getOverlayAlpha(elevation))}, ${alpha('#fff', getOverlayAlpha(elevation))})`\n        })\n      }),\n      ...other.style\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "integerPropType", "chainPropTypes", "composeClasses", "alpha", "styled", "useTheme", "memoTheme", "useDefaultProps", "getOverlayAlpha", "getPaperUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "square", "elevation", "variant", "classes", "slots", "root", "PaperRoot", "name", "slot", "overridesResolver", "props", "styles", "rounded", "theme", "backgroundColor", "vars", "palette", "background", "paper", "color", "text", "primary", "transition", "transitions", "create", "variants", "style", "borderRadius", "shape", "border", "divider", "boxShadow", "backgroundImage", "Paper", "forwardRef", "inProps", "ref", "className", "component", "other", "process", "env", "NODE_ENV", "shadows", "undefined", "console", "error", "join", "as", "overlays", "mode", "propTypes", "children", "node", "object", "string", "elementType", "Error", "bool", "sx", "oneOfType", "arrayOf", "func", "oneOf"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/Paper/Paper.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled, useTheme } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport getOverlayAlpha from \"../styles/getOverlayAlpha.js\";\nimport { getPaperUtilityClass } from \"./paperClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  color: (theme.vars || theme).palette.text.primary,\n  transition: theme.transitions.create('box-shadow'),\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.square,\n    style: {\n      borderRadius: theme.shape.borderRadius\n    }\n  }, {\n    props: {\n      variant: 'outlined'\n    },\n    style: {\n      border: `1px solid ${(theme.vars || theme).palette.divider}`\n    }\n  }, {\n    props: {\n      variant: 'elevation'\n    },\n    style: {\n      boxShadow: 'var(--Paper-shadow)',\n      backgroundImage: 'var(--Paper-overlay)'\n    }\n  }]\n})));\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const theme = useTheme();\n  const {\n    className,\n    component = 'div',\n    elevation = 1,\n    square = false,\n    variant = 'elevation',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    elevation,\n    square,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, {\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    style: {\n      ...(variant === 'elevation' && {\n        '--Paper-shadow': (theme.vars || theme).shadows[elevation],\n        ...(theme.vars && {\n          '--Paper-overlay': theme.vars.overlays?.[elevation]\n        }),\n        ...(!theme.vars && theme.palette.mode === 'dark' && {\n          '--Paper-overlay': `linear-gradient(${alpha('#fff', getOverlayAlpha(elevation))}, ${alpha('#fff', getOverlayAlpha(elevation))})`\n        })\n      }),\n      ...other.style\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,SAASC,MAAM,EAAEC,QAAQ,QAAQ,yBAAyB;AAC1D,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,SAASC,oBAAoB,QAAQ,mBAAmB;AACxD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,OAAO,EAAE,CAACF,MAAM,IAAI,SAAS,EAAEE,OAAO,KAAK,WAAW,IAAI,YAAYD,SAAS,EAAE;EAClG,CAAC;EACD,OAAOb,cAAc,CAACgB,KAAK,EAAET,oBAAoB,EAAEQ,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMG,SAAS,GAAGhB,MAAM,CAAC,KAAK,EAAE;EAC9BiB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACZ,UAAU,CAACG,OAAO,CAAC,EAAE,CAACH,UAAU,CAACC,MAAM,IAAIW,MAAM,CAACC,OAAO,EAAEb,UAAU,CAACG,OAAO,KAAK,WAAW,IAAIS,MAAM,CAAC,YAAYZ,UAAU,CAACE,SAAS,EAAE,CAAC,CAAC;EAC1K;AACF,CAAC,CAAC,CAACT,SAAS,CAAC,CAAC;EACZqB;AACF,CAAC,MAAM;EACLC,eAAe,EAAE,CAACD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACC,UAAU,CAACC,KAAK;EAC/DC,KAAK,EAAE,CAACN,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACI,IAAI,CAACC,OAAO;EACjDC,UAAU,EAAET,KAAK,CAACU,WAAW,CAACC,MAAM,CAAC,YAAY,CAAC;EAClDC,QAAQ,EAAE,CAAC;IACTf,KAAK,EAAEA,CAAC;MACNX;IACF,CAAC,KAAK,CAACA,UAAU,CAACC,MAAM;IACxB0B,KAAK,EAAE;MACLC,YAAY,EAAEd,KAAK,CAACe,KAAK,CAACD;IAC5B;EACF,CAAC,EAAE;IACDjB,KAAK,EAAE;MACLR,OAAO,EAAE;IACX,CAAC;IACDwB,KAAK,EAAE;MACLG,MAAM,EAAE,aAAa,CAAChB,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEG,OAAO,CAACc,OAAO;IAC5D;EACF,CAAC,EAAE;IACDpB,KAAK,EAAE;MACLR,OAAO,EAAE;IACX,CAAC;IACDwB,KAAK,EAAE;MACLK,SAAS,EAAE,qBAAqB;MAChCC,eAAe,EAAE;IACnB;EACF,CAAC;AACH,CAAC,CAAC,CAAC,CAAC;AACJ,MAAMC,KAAK,GAAG,aAAalD,KAAK,CAACmD,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,MAAM1B,KAAK,GAAGjB,eAAe,CAAC;IAC5BiB,KAAK,EAAEyB,OAAO;IACd5B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMM,KAAK,GAAGtB,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJ8C,SAAS;IACTC,SAAS,GAAG,KAAK;IACjBrC,SAAS,GAAG,CAAC;IACbD,MAAM,GAAG,KAAK;IACdE,OAAO,GAAG,WAAW;IACrB,GAAGqC;EACL,CAAC,GAAG7B,KAAK;EACT,MAAMX,UAAU,GAAG;IACjB,GAAGW,KAAK;IACR4B,SAAS;IACTrC,SAAS;IACTD,MAAM;IACNE;EACF,CAAC;EACD,MAAMC,OAAO,GAAGL,iBAAiB,CAACC,UAAU,CAAC;EAC7C,IAAIyC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI7B,KAAK,CAAC8B,OAAO,CAAC1C,SAAS,CAAC,KAAK2C,SAAS,EAAE;MAC1CC,OAAO,CAACC,KAAK,CAAC,CAAC,iDAAiD7C,SAAS,mCAAmC,EAAE,yCAAyCA,SAAS,iBAAiB,CAAC,CAAC8C,IAAI,CAAC,IAAI,CAAC,CAAC;IAChM;EACF;EACA,OAAO,aAAalD,IAAI,CAACS,SAAS,EAAE;IAClC0C,EAAE,EAAEV,SAAS;IACbvC,UAAU,EAAEA,UAAU;IACtBsC,SAAS,EAAEpD,IAAI,CAACkB,OAAO,CAACE,IAAI,EAAEgC,SAAS,CAAC;IACxCD,GAAG,EAAEA,GAAG;IACR,GAAGG,KAAK;IACRb,KAAK,EAAE;MACL,IAAIxB,OAAO,KAAK,WAAW,IAAI;QAC7B,gBAAgB,EAAE,CAACW,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAE8B,OAAO,CAAC1C,SAAS,CAAC;QAC1D,IAAIY,KAAK,CAACE,IAAI,IAAI;UAChB,iBAAiB,EAAEF,KAAK,CAACE,IAAI,CAACkC,QAAQ,GAAGhD,SAAS;QACpD,CAAC,CAAC;QACF,IAAI,CAACY,KAAK,CAACE,IAAI,IAAIF,KAAK,CAACG,OAAO,CAACkC,IAAI,KAAK,MAAM,IAAI;UAClD,iBAAiB,EAAE,mBAAmB7D,KAAK,CAAC,MAAM,EAAEK,eAAe,CAACO,SAAS,CAAC,CAAC,KAAKZ,KAAK,CAAC,MAAM,EAAEK,eAAe,CAACO,SAAS,CAAC,CAAC;QAC/H,CAAC;MACH,CAAC,CAAC;MACF,GAAGsC,KAAK,CAACb;IACX;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AACFc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,KAAK,CAACkB,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEpE,SAAS,CAACqE,IAAI;EACxB;AACF;AACA;EACElD,OAAO,EAAEnB,SAAS,CAACsE,MAAM;EACzB;AACF;AACA;EACEjB,SAAS,EAAErD,SAAS,CAACuE,MAAM;EAC3B;AACF;AACA;AACA;EACEjB,SAAS,EAAEtD,SAAS,CAACwE,WAAW;EAChC;AACF;AACA;AACA;AACA;EACEvD,SAAS,EAAEd,cAAc,CAACD,eAAe,EAAEwB,KAAK,IAAI;IAClD,MAAM;MACJT,SAAS;MACTC;IACF,CAAC,GAAGQ,KAAK;IACT,IAAIT,SAAS,GAAG,CAAC,IAAIC,OAAO,KAAK,UAAU,EAAE;MAC3C,OAAO,IAAIuD,KAAK,CAAC,+BAA+BxD,SAAS,uBAAuBC,OAAO,iFAAiF,CAAC;IAC3K;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEF,MAAM,EAAEhB,SAAS,CAAC0E,IAAI;EACtB;AACF;AACA;EACEhC,KAAK,EAAE1C,SAAS,CAACsE,MAAM;EACvB;AACF;AACA;EACEK,EAAE,EAAE3E,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC6E,OAAO,CAAC7E,SAAS,CAAC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAACsE,MAAM,EAAEtE,SAAS,CAAC0E,IAAI,CAAC,CAAC,CAAC,EAAE1E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAACsE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEpD,OAAO,EAAElB,SAAS,CAAC,sCAAsC4E,SAAS,CAAC,CAAC5E,SAAS,CAAC+E,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE/E,SAAS,CAACuE,MAAM,CAAC;AACnI,CAAC,GAAG,KAAK,CAAC;AACV,eAAetB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}