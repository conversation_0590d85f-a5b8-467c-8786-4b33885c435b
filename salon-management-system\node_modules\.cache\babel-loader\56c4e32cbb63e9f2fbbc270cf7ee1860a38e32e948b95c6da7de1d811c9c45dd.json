{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z\"\n}), 'ArrowDownward');", "map": {"version": 3, "names": ["React", "createSvgIcon", "jsx", "_jsx", "d"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/internal/svg-icons/ArrowDownward.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z\"\n}), 'ArrowDownward');"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,aAAa,MAAM,8BAA8B;;AAExD;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACrDC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}