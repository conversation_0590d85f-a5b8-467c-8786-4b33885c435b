{"ast": null, "code": "export { default } from \"./borders.js\";\nexport * from \"./borders.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/system/esm/borders/index.js"], "sourcesContent": ["export { default } from \"./borders.js\";\nexport * from \"./borders.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}