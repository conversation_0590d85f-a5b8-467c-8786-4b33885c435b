import React, { useState, useMemo } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import {
  Box,
  Typography,
  Paper,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Event as EventIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  AttachMoney as MoneyIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useBooking } from '../contexts/BookingContext';
import 'react-big-calendar/lib/css/react-big-calendar.css';

const localizer = momentLocalizer(moment);

const CalendarView = ({ onDateSelect, onAppointmentSelect }) => {
  const { appointments } = useBooking();
  const [selectedEvent, setSelectedEvent] = useState(null);
  const [eventDialogOpen, setEventDialogOpen] = useState(false);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [currentView, setCurrentView] = useState('week');

  // Convert appointments to calendar events
  const events = useMemo(() => {
    return appointments.map(appointment => {
      const startDate = new Date(`${appointment.date}T${appointment.time}`);
      const endDate = new Date(startDate.getTime() + appointment.duration * 60000);
      
      return {
        id: appointment.id,
        title: `${appointment.customer} - ${appointment.service}`,
        start: startDate,
        end: endDate,
        resource: appointment,
      };
    });
  }, [appointments]);

  // Custom event style based on status
  const eventStyleGetter = (event) => {
    const appointment = event.resource;
    let backgroundColor = '#3174ad';
    
    switch (appointment.status) {
      case 'completed':
        backgroundColor = '#4caf50';
        break;
      case 'in-progress':
        backgroundColor = '#ff9800';
        break;
      case 'scheduled':
        backgroundColor = '#2196f3';
        break;
      case 'cancelled':
        backgroundColor = '#f44336';
        break;
      default:
        backgroundColor = '#9e9e9e';
    }

    return {
      style: {
        backgroundColor,
        borderRadius: '4px',
        opacity: 0.8,
        color: 'white',
        border: '0px',
        display: 'block',
      },
    };
  };

  // Handle event selection
  const handleSelectEvent = (event) => {
    setSelectedEvent(event.resource);
    setEventDialogOpen(true);
    if (onAppointmentSelect) {
      onAppointmentSelect(event.resource);
    }
  };

  // Handle slot selection (for booking)
  const handleSelectSlot = (slotInfo) => {
    if (onDateSelect) {
      onDateSelect(slotInfo.start);
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'scheduled':
        return 'primary';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Handle navigation
  const handleNavigate = (action) => {
    let newDate = new Date(currentDate);

    switch (action) {
      case 'PREV':
        if (currentView === 'month') {
          newDate.setMonth(newDate.getMonth() - 1);
        } else if (currentView === 'week') {
          newDate.setDate(newDate.getDate() - 7);
        } else if (currentView === 'day') {
          newDate.setDate(newDate.getDate() - 1);
        }
        break;
      case 'NEXT':
        if (currentView === 'month') {
          newDate.setMonth(newDate.getMonth() + 1);
        } else if (currentView === 'week') {
          newDate.setDate(newDate.getDate() + 7);
        } else if (currentView === 'day') {
          newDate.setDate(newDate.getDate() + 1);
        }
        break;
      case 'TODAY':
        newDate = new Date();
        break;
      default:
        return;
    }

    setCurrentDate(newDate);
  };

  // Handle view change
  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  // Custom toolbar
  const CustomToolbar = ({ label }) => (
    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button onClick={() => handleNavigate('PREV')} variant="outlined" size="small">
          Previous
        </Button>
        <Button onClick={() => handleNavigate('TODAY')} variant="outlined" size="small">
          Today
        </Button>
        <Button onClick={() => handleNavigate('NEXT')} variant="outlined" size="small">
          Next
        </Button>
      </Box>

      <Typography variant="h6" component="div">
        {label}
      </Typography>

      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          onClick={() => handleViewChange('month')}
          variant={currentView === 'month' ? 'contained' : 'outlined'}
          size="small"
        >
          Month
        </Button>
        <Button
          onClick={() => handleViewChange('week')}
          variant={currentView === 'week' ? 'contained' : 'outlined'}
          size="small"
        >
          Week
        </Button>
        <Button
          onClick={() => handleViewChange('day')}
          variant={currentView === 'day' ? 'contained' : 'outlined'}
          size="small"
        >
          Day
        </Button>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ height: '600px', p: 2 }}>
      <Paper sx={{ p: 2, height: '100%' }}>
        <Calendar
          localizer={localizer}
          events={events}
          startAccessor="start"
          endAccessor="end"
          style={{ height: '100%' }}
          onSelectEvent={handleSelectEvent}
          onSelectSlot={handleSelectSlot}
          selectable
          eventPropGetter={eventStyleGetter}
          components={{
            toolbar: CustomToolbar,
          }}
          views={['month', 'week', 'day']}
          view={currentView}
          date={currentDate}
          onNavigate={setCurrentDate}
          onView={setCurrentView}
          step={30}
          timeslots={2}
          min={new Date(2024, 0, 1, 8, 0)} // 8 AM
          max={new Date(2024, 0, 1, 20, 0)} // 8 PM
        />
      </Paper>

      {/* Appointment Details Dialog */}
      <Dialog
        open={eventDialogOpen}
        onClose={() => setEventDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">Appointment Details</Typography>
          <IconButton onClick={() => setEventDialogOpen(false)}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        
        {selectedEvent && (
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="h6">{selectedEvent.customer}</Typography>
                      <Chip
                        label={selectedEvent.status}
                        color={getStatusColor(selectedEvent.status)}
                        size="small"
                        sx={{ ml: 'auto' }}
                      />
                    </Box>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            Service
                          </Typography>
                        </Box>
                        <Typography variant="body1">{selectedEvent.service}</Typography>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            Stylist
                          </Typography>
                        </Box>
                        <Typography variant="body1">{selectedEvent.stylist}</Typography>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            Date & Time
                          </Typography>
                        </Box>
                        <Typography variant="body1">
                          {selectedEvent.date} at {selectedEvent.time}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Duration: {selectedEvent.duration} minutes
                        </Typography>
                      </Grid>
                      
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <MoneyIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2" color="text.secondary">
                            Price
                          </Typography>
                        </Box>
                        <Typography variant="body1">${selectedEvent.price}</Typography>
                      </Grid>
                      
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">
                          Phone: {selectedEvent.phone}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </DialogContent>
        )}
        
        <DialogActions>
          <Button onClick={() => setEventDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CalendarView;
