.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Card hover effects */
.MuiCard-root:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

/* Table row hover */
.MuiTableRow-root:hover {
  background-color: rgba(0,0,0,0.04);
}

/* Custom button styles */
.MuiButton-root {
  text-transform: none;
  border-radius: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .App {
    padding: 0;
  }

  main {
    padding: 8px !important;
    margin-top: 56px !important;
  }
}

/* Compact header styles */
.MuiAppBar-root {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

.MuiToolbar-root {
  min-height: 56px !important;
}
