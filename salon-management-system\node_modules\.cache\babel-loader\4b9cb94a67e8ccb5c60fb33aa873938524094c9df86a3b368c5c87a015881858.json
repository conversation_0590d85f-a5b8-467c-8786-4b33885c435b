{"ast": null, "code": "import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { handleBreakpoints, values as breakpointsValues } from \"../breakpoints/index.js\";\nexport function sizingTransform(value) {\n  return value <= 1 && value !== 0 ? `${value * 100}%` : value;\n}\nexport const width = style({\n  prop: 'width',\n  transform: sizingTransform\n});\nexport const maxWidth = props => {\n  if (props.maxWidth !== undefined && props.maxWidth !== null) {\n    const styleFromPropValue = propValue => {\n      const breakpoint = props.theme?.breakpoints?.values?.[propValue] || breakpointsValues[propValue];\n      if (!breakpoint) {\n        return {\n          maxWidth: sizingTransform(propValue)\n        };\n      }\n      if (props.theme?.breakpoints?.unit !== 'px') {\n        return {\n          maxWidth: `${breakpoint}${props.theme.breakpoints.unit}`\n        };\n      }\n      return {\n        maxWidth: breakpoint\n      };\n    };\n    return handleBreakpoints(props, props.maxWidth, styleFromPropValue);\n  }\n  return null;\n};\nmaxWidth.filterProps = ['maxWidth'];\nexport const minWidth = style({\n  prop: 'minWidth',\n  transform: sizingTransform\n});\nexport const height = style({\n  prop: 'height',\n  transform: sizingTransform\n});\nexport const maxHeight = style({\n  prop: 'maxHeight',\n  transform: sizingTransform\n});\nexport const minHeight = style({\n  prop: 'minHeight',\n  transform: sizingTransform\n});\nexport const sizeWidth = style({\n  prop: 'size',\n  cssProperty: 'width',\n  transform: sizingTransform\n});\nexport const sizeHeight = style({\n  prop: 'size',\n  cssProperty: 'height',\n  transform: sizingTransform\n});\nexport const boxSizing = style({\n  prop: 'boxSizing'\n});\nconst sizing = compose(width, maxWidth, minWidth, height, maxHeight, minHeight, boxSizing);\nexport default sizing;", "map": {"version": 3, "names": ["style", "compose", "handleBreakpoints", "values", "breakpointsValues", "sizingTransform", "value", "width", "prop", "transform", "max<PERSON><PERSON><PERSON>", "props", "undefined", "styleFromPropValue", "propValue", "breakpoint", "theme", "breakpoints", "unit", "filterProps", "min<PERSON><PERSON><PERSON>", "height", "maxHeight", "minHeight", "sizeWidth", "cssProperty", "sizeHeight", "boxSizing", "sizing"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/system/esm/sizing/sizing.js"], "sourcesContent": ["import style from \"../style/index.js\";\nimport compose from \"../compose/index.js\";\nimport { handleBreakpoints, values as breakpointsValues } from \"../breakpoints/index.js\";\nexport function sizingTransform(value) {\n  return value <= 1 && value !== 0 ? `${value * 100}%` : value;\n}\nexport const width = style({\n  prop: 'width',\n  transform: sizingTransform\n});\nexport const maxWidth = props => {\n  if (props.maxWidth !== undefined && props.maxWidth !== null) {\n    const styleFromPropValue = propValue => {\n      const breakpoint = props.theme?.breakpoints?.values?.[propValue] || breakpointsValues[propValue];\n      if (!breakpoint) {\n        return {\n          maxWidth: sizingTransform(propValue)\n        };\n      }\n      if (props.theme?.breakpoints?.unit !== 'px') {\n        return {\n          maxWidth: `${breakpoint}${props.theme.breakpoints.unit}`\n        };\n      }\n      return {\n        maxWidth: breakpoint\n      };\n    };\n    return handleBreakpoints(props, props.maxWidth, styleFromPropValue);\n  }\n  return null;\n};\nmaxWidth.filterProps = ['maxWidth'];\nexport const minWidth = style({\n  prop: 'minWidth',\n  transform: sizingTransform\n});\nexport const height = style({\n  prop: 'height',\n  transform: sizingTransform\n});\nexport const maxHeight = style({\n  prop: 'maxHeight',\n  transform: sizingTransform\n});\nexport const minHeight = style({\n  prop: 'minHeight',\n  transform: sizingTransform\n});\nexport const sizeWidth = style({\n  prop: 'size',\n  cssProperty: 'width',\n  transform: sizingTransform\n});\nexport const sizeHeight = style({\n  prop: 'size',\n  cssProperty: 'height',\n  transform: sizingTransform\n});\nexport const boxSizing = style({\n  prop: 'boxSizing'\n});\nconst sizing = compose(width, maxWidth, minWidth, height, maxHeight, minHeight, boxSizing);\nexport default sizing;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,SAASC,iBAAiB,EAAEC,MAAM,IAAIC,iBAAiB,QAAQ,yBAAyB;AACxF,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,KAAK,CAAC,GAAG,GAAGA,KAAK,GAAG,GAAG,GAAG,GAAGA,KAAK;AAC9D;AACA,OAAO,MAAMC,KAAK,GAAGP,KAAK,CAAC;EACzBQ,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMK,QAAQ,GAAGC,KAAK,IAAI;EAC/B,IAAIA,KAAK,CAACD,QAAQ,KAAKE,SAAS,IAAID,KAAK,CAACD,QAAQ,KAAK,IAAI,EAAE;IAC3D,MAAMG,kBAAkB,GAAGC,SAAS,IAAI;MACtC,MAAMC,UAAU,GAAGJ,KAAK,CAACK,KAAK,EAAEC,WAAW,EAAEd,MAAM,GAAGW,SAAS,CAAC,IAAIV,iBAAiB,CAACU,SAAS,CAAC;MAChG,IAAI,CAACC,UAAU,EAAE;QACf,OAAO;UACLL,QAAQ,EAAEL,eAAe,CAACS,SAAS;QACrC,CAAC;MACH;MACA,IAAIH,KAAK,CAACK,KAAK,EAAEC,WAAW,EAAEC,IAAI,KAAK,IAAI,EAAE;QAC3C,OAAO;UACLR,QAAQ,EAAE,GAAGK,UAAU,GAAGJ,KAAK,CAACK,KAAK,CAACC,WAAW,CAACC,IAAI;QACxD,CAAC;MACH;MACA,OAAO;QACLR,QAAQ,EAAEK;MACZ,CAAC;IACH,CAAC;IACD,OAAOb,iBAAiB,CAACS,KAAK,EAAEA,KAAK,CAACD,QAAQ,EAAEG,kBAAkB,CAAC;EACrE;EACA,OAAO,IAAI;AACb,CAAC;AACDH,QAAQ,CAACS,WAAW,GAAG,CAAC,UAAU,CAAC;AACnC,OAAO,MAAMC,QAAQ,GAAGpB,KAAK,CAAC;EAC5BQ,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMgB,MAAM,GAAGrB,KAAK,CAAC;EAC1BQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMiB,SAAS,GAAGtB,KAAK,CAAC;EAC7BQ,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMkB,SAAS,GAAGvB,KAAK,CAAC;EAC7BQ,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMmB,SAAS,GAAGxB,KAAK,CAAC;EAC7BQ,IAAI,EAAE,MAAM;EACZiB,WAAW,EAAE,OAAO;EACpBhB,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMqB,UAAU,GAAG1B,KAAK,CAAC;EAC9BQ,IAAI,EAAE,MAAM;EACZiB,WAAW,EAAE,QAAQ;EACrBhB,SAAS,EAAEJ;AACb,CAAC,CAAC;AACF,OAAO,MAAMsB,SAAS,GAAG3B,KAAK,CAAC;EAC7BQ,IAAI,EAAE;AACR,CAAC,CAAC;AACF,MAAMoB,MAAM,GAAG3B,OAAO,CAACM,KAAK,EAAEG,QAAQ,EAAEU,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEI,SAAS,CAAC;AAC1F,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}