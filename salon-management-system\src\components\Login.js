import React, { useState } from 'react';
import {
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Box,
  Alert,
  Tabs,
  Tab,
  InputAdornment,
  IconButton,
  Divider,
  Link
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Person,
  AdminPanelSettings,
  Groups
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Login = () => {
  const [tabValue, setTabValue] = useState(0);
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  
  const { login } = useAuth();
  const navigate = useNavigate();

  const userTypes = ['staff', 'admin', 'customer'];
  const tabLabels = ['Staff Login', 'Admin Login', 'Customer Login'];
  const tabIcons = [<Groups />, <AdminPanelSettings />, <Person />];

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    setError('');
    setFormData({ username: '', password: '' });
  };

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    if (!formData.username || !formData.password) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    const result = await login(formData.username, formData.password, userTypes[tabValue]);
    
    if (result.success) {
      // Redirect based on user role
      if (result.user.role === 'admin') {
        navigate('/');
      } else if (result.user.role === 'staff') {
        navigate('/');
      } else if (result.user.role === 'customer') {
        navigate('/appointments');
      }
    } else {
      setError(result.error);
    }
    
    setLoading(false);
  };

  const handleRegisterRedirect = () => {
    navigate('/register');
  };

  // Demo credentials info
  const getDemoCredentials = () => {
    switch (tabValue) {
      case 0: // Staff
        return [
          { username: 'stylist1', password: 'staff123', role: 'Senior Stylist' },
          { username: 'receptionist1', password: 'staff123', role: 'Receptionist' }
        ];
      case 1: // Admin
        return [{ username: 'admin', password: 'admin123', role: 'Administrator' }];
      case 2: // Customer
        return [{ username: 'customer1', password: 'customer123', role: 'Customer' }];
      default:
        return [];
    }
  };

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
          <Typography component="h1" variant="h4" align="center" gutterBottom>
            Salon Management System
          </Typography>
          
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ mb: 3 }}
          >
            {tabLabels.map((label, index) => (
              <Tab
                key={index}
                label={label}
                icon={tabIcons[index]}
                iconPosition="start"
                sx={{ minHeight: 60 }}
              />
            ))}
          </Tabs>

          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <TextField
              margin="normal"
              required
              fullWidth
              id="username"
              label="Username"
              name="username"
              autoComplete="username"
              autoFocus
              value={formData.username}
              onChange={handleInputChange}
            />

            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type={showPassword ? 'text' : 'password'}
              id="password"
              autoComplete="current-password"
              value={formData.password}
              onChange={handleInputChange}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 3, mb: 2 }}
              disabled={loading}
            >
              {loading ? 'Signing In...' : 'Sign In'}
            </Button>

            {tabValue === 2 && (
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="body2">
                  Don't have an account?{' '}
                  <Link
                    component="button"
                    variant="body2"
                    onClick={handleRegisterRedirect}
                    sx={{ textDecoration: 'none' }}
                  >
                    Register here
                  </Link>
                </Typography>
              </Box>
            )}

            <Divider sx={{ my: 3 }} />

            {/* Demo Credentials */}
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Demo Credentials:
              </Typography>
              {getDemoCredentials().map((cred, index) => (
                <Box key={index} sx={{ mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>{cred.role}:</strong> {cred.username} / {cred.password}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default Login;
