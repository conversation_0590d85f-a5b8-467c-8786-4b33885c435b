{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTableContainerUtilityClass } from \"./tableContainerClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTableContainerUtilityClass, classes);\n};\nconst TableContainerRoot = styled('div', {\n  name: 'MuiTableContainer',\n  slot: 'Root'\n})({\n  width: '100%',\n  overflowX: 'auto'\n});\nconst TableContainer = /*#__PURE__*/React.forwardRef(function TableContainer(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableContainer'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TableContainerRoot, {\n    ref: ref,\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TableContainer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `Table`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableContainer;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "useDefaultProps", "getTableContainerUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "TableContainerRoot", "name", "slot", "width", "overflowX", "TableContainer", "forwardRef", "inProps", "ref", "props", "className", "component", "other", "as", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "elementType", "sx", "oneOfType", "arrayOf", "func", "bool"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/TableContainer/TableContainer.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTableContainerUtilityClass } from \"./tableContainerClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getTableContainerUtilityClass, classes);\n};\nconst TableContainerRoot = styled('div', {\n  name: 'MuiTableContainer',\n  slot: 'Root'\n})({\n  width: '100%',\n  overflowX: 'auto'\n});\nconst TableContainer = /*#__PURE__*/React.forwardRef(function TableContainer(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTableContainer'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(TableContainerRoot, {\n    ref: ref,\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? TableContainer.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `Table`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default TableContainer;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,6BAA6B,QAAQ,4BAA4B;AAC1E,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOV,cAAc,CAACS,KAAK,EAAEN,6BAA6B,EAAEK,OAAO,CAAC;AACtE,CAAC;AACD,MAAMG,kBAAkB,GAAGV,MAAM,CAAC,KAAK,EAAE;EACvCW,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC;EACDC,KAAK,EAAE,MAAM;EACbC,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMC,cAAc,GAAG,aAAanB,KAAK,CAACoB,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMC,KAAK,GAAGlB,eAAe,CAAC;IAC5BkB,KAAK,EAAEF,OAAO;IACdN,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJS,SAAS;IACTC,SAAS,GAAG,KAAK;IACjB,GAAGC;EACL,CAAC,GAAGH,KAAK;EACT,MAAMb,UAAU,GAAG;IACjB,GAAGa,KAAK;IACRE;EACF,CAAC;EACD,MAAMd,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,kBAAkB,EAAE;IAC3CQ,GAAG,EAAEA,GAAG;IACRK,EAAE,EAAEF,SAAS;IACbD,SAAS,EAAEtB,IAAI,CAACS,OAAO,CAACE,IAAI,EAAEW,SAAS,CAAC;IACxCd,UAAU,EAAEA,UAAU;IACtB,GAAGgB;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,cAAc,CAACY,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAE/B,SAAS,CAACgC,IAAI;EACxB;AACF;AACA;EACEtB,OAAO,EAAEV,SAAS,CAACiC,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAEvB,SAAS,CAACkC,MAAM;EAC3B;AACF;AACA;AACA;EACEV,SAAS,EAAExB,SAAS,CAACmC,WAAW;EAChC;AACF;AACA;EACEC,EAAE,EAAEpC,SAAS,CAACqC,SAAS,CAAC,CAACrC,SAAS,CAACsC,OAAO,CAACtC,SAAS,CAACqC,SAAS,CAAC,CAACrC,SAAS,CAACuC,IAAI,EAAEvC,SAAS,CAACiC,MAAM,EAAEjC,SAAS,CAACwC,IAAI,CAAC,CAAC,CAAC,EAAExC,SAAS,CAACuC,IAAI,EAAEvC,SAAS,CAACiC,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAef,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}