{"ast": null, "code": "export { default } from \"./StyledEngineProvider.js\";", "map": {"version": 3, "names": ["default"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/styled-engine/esm/StyledEngineProvider/index.js"], "sourcesContent": ["export { default } from \"./StyledEngineProvider.js\";"], "mappings": "AAAA,SAASA,OAAO,QAAQ,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}