import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Tabs,
  Tab,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Cancel as CancelIcon,
  CalendarToday as CalendarIcon,
  List as ListIcon,
} from '@mui/icons-material';
import { BookingProvider, useBooking } from '../contexts/BookingContext';
import CalendarView from './CalendarView';
import BookingFlow from './BookingFlow';
import CancelBookingDialog from './CancelBookingDialog';

const AppointmentsContent = () => {
  const { appointments, setAppointments } = useBooking();

  const [currentTab, setCurrentTab] = useState(0);
  const [open, setOpen] = useState(false);
  const [bookingDialogOpen, setBookingDialogOpen] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [editingAppointment, setEditingAppointment] = useState(null);
  const [formData, setFormData] = useState({
    customer: '',
    phone: '',
    service: '',
    stylist: '',
    date: '',
    time: '',
    duration: '',
    price: '',
    status: 'scheduled',
  });

  const services = [
    'Hair Cut & Style',
    'Hair Color',
    'Beard Trim',
    'Full Service',
    'Manicure',
    'Pedicure',
    'Facial',
    'Massage',
  ];

  const stylists = [
    'Emma Wilson',
    'John Smith',
    'Mike Johnson',
    'Sarah Davis',
    'Lisa Anderson',
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'scheduled':
        return 'primary';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  const handleOpen = (appointment = null) => {
    if (appointment) {
      setEditingAppointment(appointment);
      setFormData(appointment);
    } else {
      setEditingAppointment(null);
      setFormData({
        customer: '',
        phone: '',
        service: '',
        stylist: '',
        date: '',
        time: '',
        duration: '',
        price: '',
        status: 'scheduled',
      });
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setEditingAppointment(null);
  };

  const handleSave = () => {
    if (editingAppointment) {
      setAppointments(appointments.map(apt => 
        apt.id === editingAppointment.id ? { ...formData, id: editingAppointment.id } : apt
      ));
    } else {
      const newAppointment = {
        ...formData,
        id: Math.max(...appointments.map(a => a.id)) + 1,
      };
      setAppointments([...appointments, newAppointment]);
    }
    handleClose();
  };

  const handleDelete = (id) => {
    setAppointments(appointments.filter(apt => apt.id !== id));
  };

  const handleCancel = (appointment) => {
    setSelectedAppointment(appointment);
    setCancelDialogOpen(true);
  };

  const handleCancelConfirm = (appointment, reason) => {
    console.log(`Appointment ${appointment.id} cancelled. Reason: ${reason}`);
    setCancelDialogOpen(false);
    setSelectedAppointment(null);
  };

  const handleInputChange = (field, value) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleNewBooking = () => {
    setBookingDialogOpen(true);
  };

  const handleBookingComplete = (appointment) => {
    setBookingDialogOpen(false);
    // Appointment is already added to the context by the booking flow
  };

  const today = new Date().toISOString().split('T')[0]; // Get today's date in YYYY-MM-DD format

  const todayStats = {
    total: appointments.filter(apt => apt.date === today).length,
    completed: appointments.filter(apt => apt.date === today && apt.status === 'completed').length,
    scheduled: appointments.filter(apt => apt.date === today && apt.status === 'scheduled').length,
    inProgress: appointments.filter(apt => apt.date === today && apt.status === 'in-progress').length,
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Appointments</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleNewBooking}
        >
          Book Appointment
        </Button>
      </Box>

      {/* Tabs for different views */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={handleTabChange}>
          <Tab icon={<CalendarIcon />} label="Calendar View" />
          <Tab icon={<ListIcon />} label="List View" />
        </Tabs>
      </Box>

      {/* Today's Stats */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Total Today
              </Typography>
              <Typography variant="h4">
                {todayStats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Completed
              </Typography>
              <Typography variant="h4" color="success.main">
                {todayStats.completed}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                In Progress
              </Typography>
              <Typography variant="h4" color="warning.main">
                {todayStats.inProgress}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                Scheduled
              </Typography>
              <Typography variant="h4" color="primary.main">
                {todayStats.scheduled}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tab Content */}
      {currentTab === 0 && (
        <CalendarView
          onDateSelect={(date) => {
            // Handle date selection for new booking
            console.log('Date selected:', date);
          }}
          onAppointmentSelect={(appointment) => {
            setSelectedAppointment(appointment);
          }}
        />
      )}

      {currentTab === 1 && (
        <>
          {/* Appointments Table */}
          <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Customer</TableCell>
                <TableCell>Phone</TableCell>
                <TableCell>Service</TableCell>
                <TableCell>Stylist</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Time</TableCell>
                <TableCell>Duration</TableCell>
                <TableCell>Price</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {appointments.map((appointment) => (
                <TableRow key={appointment.id}>
                  <TableCell>{appointment.customer}</TableCell>
                  <TableCell>{appointment.phone}</TableCell>
                  <TableCell>{appointment.service}</TableCell>
                  <TableCell>{appointment.stylist}</TableCell>
                  <TableCell>{appointment.date}</TableCell>
                  <TableCell>{appointment.time}</TableCell>
                  <TableCell>{appointment.duration} min</TableCell>
                  <TableCell>${appointment.price}</TableCell>
                  <TableCell>
                    <Chip
                      label={appointment.status}
                      color={getStatusColor(appointment.status)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleOpen(appointment)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    {appointment.status !== 'cancelled' && appointment.status !== 'completed' && (
                      <IconButton
                        size="small"
                        onClick={() => handleCancel(appointment)}
                        color="warning"
                      >
                        <CancelIcon />
                      </IconButton>
                    )}
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(appointment.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          </TableContainer>
        </>
      )}

      {/* Add/Edit Dialog */}
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingAppointment ? 'Edit Appointment' : 'New Appointment'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Customer Name"
                value={formData.customer}
                onChange={(e) => handleInputChange('customer', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Service"
                value={formData.service}
                onChange={(e) => handleInputChange('service', e.target.value)}
              >
                {services.map((service) => (
                  <MenuItem key={service} value={service}>
                    {service}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                select
                label="Stylist"
                value={formData.stylist}
                onChange={(e) => handleInputChange('stylist', e.target.value)}
              >
                {stylists.map((stylist) => (
                  <MenuItem key={stylist} value={stylist}>
                    {stylist}
                  </MenuItem>
                ))}
              </TextField>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="date"
                label="Date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                type="time"
                label="Time"
                value={formData.time}
                onChange={(e) => handleInputChange('time', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Duration"
                value={formData.duration}
                onChange={(e) => handleInputChange('duration', e.target.value)}
                placeholder="e.g., 60 min"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Price"
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="e.g., $85"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                select
                label="Status"
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
              >
                <MenuItem value="scheduled">Scheduled</MenuItem>
                <MenuItem value="in-progress">In Progress</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
                <MenuItem value="cancelled">Cancelled</MenuItem>
              </TextField>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSave} variant="contained">
            {editingAppointment ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Booking Flow Dialog */}
      <Dialog
        open={bookingDialogOpen}
        onClose={() => setBookingDialogOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { minHeight: '80vh' }
        }}
      >
        <DialogContent sx={{ p: 0 }}>
          <BookingFlow onBookingComplete={handleBookingComplete} />
        </DialogContent>
      </Dialog>

      {/* Cancel Booking Dialog */}
      <CancelBookingDialog
        open={cancelDialogOpen}
        onClose={() => setCancelDialogOpen(false)}
        appointment={selectedAppointment}
        onCancel={handleCancelConfirm}
      />
    </Box>
  );
};

// Main component with BookingProvider
const Appointments = () => {
  return (
    <BookingProvider>
      <AppointmentsContent />
    </BookingProvider>
  );
};

export default Appointments;
