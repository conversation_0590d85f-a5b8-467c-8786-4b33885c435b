{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTablePaginationActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiTablePaginationActions', slot);\n}\nconst tablePaginationActionsClasses = generateUtilityClasses('MuiTablePaginationActions', ['root']);\nexport default tablePaginationActionsClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTablePaginationActionsUtilityClass", "slot", "tablePaginationActionsClasses"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/TablePaginationActions/tablePaginationActionsClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTablePaginationActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiTablePaginationActions', slot);\n}\nconst tablePaginationActionsClasses = generateUtilityClasses('MuiTablePaginationActions', ['root']);\nexport default tablePaginationActionsClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,qCAAqCA,CAACC,IAAI,EAAE;EAC1D,OAAOF,oBAAoB,CAAC,2BAA2B,EAAEE,IAAI,CAAC;AAChE;AACA,MAAMC,6BAA6B,GAAGJ,sBAAsB,CAAC,2BAA2B,EAAE,CAAC,MAAM,CAAC,CAAC;AACnG,eAAeI,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}