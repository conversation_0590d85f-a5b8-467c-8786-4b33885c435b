{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { AppBar, Toolbar, Typography, Button, Box, useTheme, useMediaQuery } from '@mui/material';\nimport { ContentCut as SalonIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Navbar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const menuItems = [{\n    text: 'Dashboard',\n    path: '/'\n  }, {\n    text: 'Appointments',\n    path: '/appointments'\n  }, {\n    text: 'Customers',\n    path: '/customers'\n  }, {\n    text: 'Services',\n    path: '/services'\n  }, {\n    text: 'Staff',\n    path: '/staff'\n  }, {\n    text: 'Reports',\n    path: '/reports'\n  }];\n  const handleNavigation = path => {\n    navigate(path);\n  };\n  const drawer = /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: 250\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(SalonIcon, {\n        color: \"primary\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"primary\",\n        children: \"Salon Manager\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      children: menuItems.map(item => /*#__PURE__*/_jsxDEV(ListItem, {\n        button: true,\n        onClick: () => handleNavigation(item.path),\n        selected: location.pathname === item.path,\n        sx: {\n          '&.Mui-selected': {\n            backgroundColor: theme.palette.primary.light + '20',\n            '&:hover': {\n              backgroundColor: theme.palette.primary.light + '30'\n            }\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n          sx: {\n            color: location.pathname === item.path ? theme.palette.primary.main : 'inherit'\n          },\n          children: item.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n          primary: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this)]\n      }, item.text, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"fixed\",\n      sx: {\n        zIndex: theme.zIndex.drawer + 1\n      },\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [isMobile && /*#__PURE__*/_jsxDEV(IconButton, {\n          color: \"inherit\",\n          \"aria-label\": \"open drawer\",\n          edge: \"start\",\n          onClick: handleDrawerToggle,\n          sx: {\n            mr: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(MenuIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(SalonIcon, {\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          sx: {\n            flexGrow: 1\n          },\n          children: \"Salon Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), !isMobile && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1\n          },\n          children: menuItems.map(item => /*#__PURE__*/_jsxDEV(Button, {\n            color: \"inherit\",\n            onClick: () => handleNavigation(item.path),\n            sx: {\n              backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.1)' : 'transparent',\n              '&:hover': {\n                backgroundColor: 'rgba(255,255,255,0.2)'\n              }\n            },\n            children: item.text\n          }, item.text, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), isMobile && /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"temporary\",\n      open: mobileOpen,\n      onClose: handleDrawerToggle,\n      ModalProps: {\n        keepMounted: true\n      },\n      sx: {\n        '& .MuiDrawer-paper': {\n          boxSizing: 'border-box',\n          width: 250\n        }\n      },\n      children: drawer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this), !isMobile && /*#__PURE__*/_jsxDEV(Drawer, {\n      variant: \"permanent\",\n      sx: {\n        width: 250,\n        flexShrink: 0,\n        '& .MuiDrawer-paper': {\n          width: 250,\n          boxSizing: 'border-box',\n          top: '64px',\n          height: 'calc(100% - 64px)'\n        }\n      },\n      children: drawer\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(Navbar, \"U1Fp97TqvWKdCm8vEKOq+ut6eTU=\", false, function () {\n  return [useNavigate, useLocation, useTheme, useMediaQuery];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "Box", "useTheme", "useMediaQuery", "ContentCut", "SalonIcon", "useNavigate", "useLocation", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "navigate", "location", "theme", "isMobile", "breakpoints", "down", "menuItems", "text", "path", "handleNavigation", "drawer", "sx", "width", "children", "p", "display", "alignItems", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "List", "map", "item", "ListItem", "button", "onClick", "selected", "pathname", "backgroundColor", "palette", "primary", "light", "ListItemIcon", "main", "icon", "ListItemText", "position", "zIndex", "IconButton", "edge", "handleDrawerToggle", "mr", "MenuIcon", "component", "flexGrow", "Drawer", "open", "mobileOpen", "onClose", "ModalProps", "keepMounted", "boxSizing", "flexShrink", "top", "height", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport {\n  AppBar,\n  Too<PERSON>bar,\n  Typography,\n  Button,\n  Box,\n  useTheme,\n  useMediaQuery,\n} from '@mui/material';\nimport {\n  ContentCut as SalonIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\n\nconst Navbar = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n\n  const menuItems = [\n    { text: 'Dashboard', path: '/' },\n    { text: 'Appointments', path: '/appointments' },\n    { text: 'Customers', path: '/customers' },\n    { text: 'Services', path: '/services' },\n    { text: 'Staff', path: '/staff' },\n    { text: 'Reports', path: '/reports' },\n  ];\n\n  const handleNavigation = (path) => {\n    navigate(path);\n  };\n\n  const drawer = (\n    <Box sx={{ width: 250 }}>\n      <Box sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 1 }}>\n        <SalonIcon color=\"primary\" />\n        <Typography variant=\"h6\" color=\"primary\">\n          Salon Manager\n        </Typography>\n      </Box>\n      <List>\n        {menuItems.map((item) => (\n          <ListItem\n            button\n            key={item.text}\n            onClick={() => handleNavigation(item.path)}\n            selected={location.pathname === item.path}\n            sx={{\n              '&.Mui-selected': {\n                backgroundColor: theme.palette.primary.light + '20',\n                '&:hover': {\n                  backgroundColor: theme.palette.primary.light + '30',\n                },\n              },\n            }}\n          >\n            <ListItemIcon sx={{ color: location.pathname === item.path ? theme.palette.primary.main : 'inherit' }}>\n              {item.icon}\n            </ListItemIcon>\n            <ListItemText primary={item.text} />\n          </ListItem>\n        ))}\n      </List>\n    </Box>\n  );\n\n  return (\n    <>\n      <AppBar position=\"fixed\" sx={{ zIndex: theme.zIndex.drawer + 1 }}>\n        <Toolbar>\n          {isMobile && (\n            <IconButton\n              color=\"inherit\"\n              aria-label=\"open drawer\"\n              edge=\"start\"\n              onClick={handleDrawerToggle}\n              sx={{ mr: 2 }}\n            >\n              <MenuIcon />\n            </IconButton>\n          )}\n          <SalonIcon sx={{ mr: 1 }} />\n          <Typography variant=\"h6\" component=\"div\" sx={{ flexGrow: 1 }}>\n            Salon Management System\n          </Typography>\n          {!isMobile && (\n            <Box sx={{ display: 'flex', gap: 1 }}>\n              {menuItems.map((item) => (\n                <Button\n                  key={item.text}\n                  color=\"inherit\"\n                  onClick={() => handleNavigation(item.path)}\n                  sx={{\n                    backgroundColor: location.pathname === item.path ? 'rgba(255,255,255,0.1)' : 'transparent',\n                    '&:hover': {\n                      backgroundColor: 'rgba(255,255,255,0.2)',\n                    },\n                  }}\n                >\n                  {item.text}\n                </Button>\n              ))}\n            </Box>\n          )}\n        </Toolbar>\n      </AppBar>\n\n      {isMobile && (\n        <Drawer\n          variant=\"temporary\"\n          open={mobileOpen}\n          onClose={handleDrawerToggle}\n          ModalProps={{\n            keepMounted: true,\n          }}\n          sx={{\n            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 250 },\n          }}\n        >\n          {drawer}\n        </Drawer>\n      )}\n\n      {!isMobile && (\n        <Drawer\n          variant=\"permanent\"\n          sx={{\n            width: 250,\n            flexShrink: 0,\n            '& .MuiDrawer-paper': {\n              width: 250,\n              boxSizing: 'border-box',\n              top: '64px',\n              height: 'calc(100% - 64px)',\n            },\n          }}\n        >\n          {drawer}\n        </Drawer>\n      )}\n    </>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,SACEC,UAAU,IAAIC,SAAS,QAClB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5D,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,KAAK,GAAGd,QAAQ,CAAC,CAAC;EACxB,MAAMe,QAAQ,GAAGd,aAAa,CAACa,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAE5D,MAAMC,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAI,CAAC,EAChC;IAAED,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE;EAAgB,CAAC,EAC/C;IAAED,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE;EAAa,CAAC,EACzC;IAAED,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE;EAAY,CAAC,EACvC;IAAED,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE;EAAS,CAAC,EACjC;IAAED,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE;EAAW,CAAC,CACtC;EAED,MAAMC,gBAAgB,GAAID,IAAI,IAAK;IACjCR,QAAQ,CAACQ,IAAI,CAAC;EAChB,CAAC;EAED,MAAME,MAAM,gBACVf,OAAA,CAACR,GAAG;IAACwB,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE;IAAAC,QAAA,gBACtBlB,OAAA,CAACR,GAAG;MAACwB,EAAE,EAAE;QAAEG,CAAC,EAAE,CAAC;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAC/DlB,OAAA,CAACJ,SAAS;QAAC2B,KAAK,EAAC;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7B3B,OAAA,CAACV,UAAU;QAACsC,OAAO,EAAC,IAAI;QAACL,KAAK,EAAC,SAAS;QAAAL,QAAA,EAAC;MAEzC;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eACN3B,OAAA,CAAC6B,IAAI;MAAAX,QAAA,EACFP,SAAS,CAACmB,GAAG,CAAEC,IAAI,iBAClB/B,OAAA,CAACgC,QAAQ;QACPC,MAAM;QAENC,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACiB,IAAI,CAAClB,IAAI,CAAE;QAC3CsB,QAAQ,EAAE7B,QAAQ,CAAC8B,QAAQ,KAAKL,IAAI,CAAClB,IAAK;QAC1CG,EAAE,EAAE;UACF,gBAAgB,EAAE;YAChBqB,eAAe,EAAE9B,KAAK,CAAC+B,OAAO,CAACC,OAAO,CAACC,KAAK,GAAG,IAAI;YACnD,SAAS,EAAE;cACTH,eAAe,EAAE9B,KAAK,CAAC+B,OAAO,CAACC,OAAO,CAACC,KAAK,GAAG;YACjD;UACF;QACF,CAAE;QAAAtB,QAAA,gBAEFlB,OAAA,CAACyC,YAAY;UAACzB,EAAE,EAAE;YAAEO,KAAK,EAAEjB,QAAQ,CAAC8B,QAAQ,KAAKL,IAAI,CAAClB,IAAI,GAAGN,KAAK,CAAC+B,OAAO,CAACC,OAAO,CAACG,IAAI,GAAG;UAAU,CAAE;UAAAxB,QAAA,EACnGa,IAAI,CAACY;QAAI;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACf3B,OAAA,CAAC4C,YAAY;UAACL,OAAO,EAAER,IAAI,CAACnB;QAAK;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,GAf/BI,IAAI,CAACnB,IAAI;QAAAY,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAgBN,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CACN;EAED,oBACE3B,OAAA,CAAAE,SAAA;IAAAgB,QAAA,gBACElB,OAAA,CAACZ,MAAM;MAACyD,QAAQ,EAAC,OAAO;MAAC7B,EAAE,EAAE;QAAE8B,MAAM,EAAEvC,KAAK,CAACuC,MAAM,CAAC/B,MAAM,GAAG;MAAE,CAAE;MAAAG,QAAA,eAC/DlB,OAAA,CAACX,OAAO;QAAA6B,QAAA,GACLV,QAAQ,iBACPR,OAAA,CAAC+C,UAAU;UACTxB,KAAK,EAAC,SAAS;UACf,cAAW,aAAa;UACxByB,IAAI,EAAC,OAAO;UACZd,OAAO,EAAEe,kBAAmB;UAC5BjC,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAE,CAAE;UAAAhC,QAAA,eAEdlB,OAAA,CAACmD,QAAQ;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACb,eACD3B,OAAA,CAACJ,SAAS;UAACoB,EAAE,EAAE;YAAEkC,EAAE,EAAE;UAAE;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5B3B,OAAA,CAACV,UAAU;UAACsC,OAAO,EAAC,IAAI;UAACwB,SAAS,EAAC,KAAK;UAACpC,EAAE,EAAE;YAAEqC,QAAQ,EAAE;UAAE,CAAE;UAAAnC,QAAA,EAAC;QAE9D;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ,CAACnB,QAAQ,iBACRR,OAAA,CAACR,GAAG;UAACwB,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEE,GAAG,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAClCP,SAAS,CAACmB,GAAG,CAAEC,IAAI,iBAClB/B,OAAA,CAACT,MAAM;YAELgC,KAAK,EAAC,SAAS;YACfW,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACiB,IAAI,CAAClB,IAAI,CAAE;YAC3CG,EAAE,EAAE;cACFqB,eAAe,EAAE/B,QAAQ,CAAC8B,QAAQ,KAAKL,IAAI,CAAClB,IAAI,GAAG,uBAAuB,GAAG,aAAa;cAC1F,SAAS,EAAE;gBACTwB,eAAe,EAAE;cACnB;YACF,CAAE;YAAAnB,QAAA,EAEDa,IAAI,CAACnB;UAAI,GAVLmB,IAAI,CAACnB,IAAI;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWR,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAERnB,QAAQ,iBACPR,OAAA,CAACsD,MAAM;MACL1B,OAAO,EAAC,WAAW;MACnB2B,IAAI,EAAEC,UAAW;MACjBC,OAAO,EAAER,kBAAmB;MAC5BS,UAAU,EAAE;QACVC,WAAW,EAAE;MACf,CAAE;MACF3C,EAAE,EAAE;QACF,oBAAoB,EAAE;UAAE4C,SAAS,EAAE,YAAY;UAAE3C,KAAK,EAAE;QAAI;MAC9D,CAAE;MAAAC,QAAA,EAEDH;IAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACT,EAEA,CAACnB,QAAQ,iBACRR,OAAA,CAACsD,MAAM;MACL1B,OAAO,EAAC,WAAW;MACnBZ,EAAE,EAAE;QACFC,KAAK,EAAE,GAAG;QACV4C,UAAU,EAAE,CAAC;QACb,oBAAoB,EAAE;UACpB5C,KAAK,EAAE,GAAG;UACV2C,SAAS,EAAE,YAAY;UACvBE,GAAG,EAAE,MAAM;UACXC,MAAM,EAAE;QACV;MACF,CAAE;MAAA7C,QAAA,EAEDH;IAAM;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACT;EAAA,eACD,CAAC;AAEP,CAAC;AAACvB,EAAA,CAjIID,MAAM;EAAA,QACON,WAAW,EACXC,WAAW,EACdL,QAAQ,EACLC,aAAa;AAAA;AAAAsE,EAAA,GAJ1B7D,MAAM;AAmIZ,eAAeA,MAAM;AAAC,IAAA6D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}