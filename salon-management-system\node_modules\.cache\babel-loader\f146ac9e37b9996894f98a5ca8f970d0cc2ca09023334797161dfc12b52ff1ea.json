{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { html, body } from \"../CssBaseline/CssBaseline.js\";\nimport { getScopedCssBaselineUtilityClass } from \"./scopedCssBaselineClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getScopedCssBaselineUtilityClass, classes);\n};\nconst ScopedCssBaselineRoot = styled('div', {\n  name: 'MuiScopedCssBaseline',\n  slot: 'Root'\n})(memoTheme(({\n  theme\n}) => {\n  const colorSchemeStyles = {};\n  if (theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        colorSchemeStyles[selector] = {\n          colorScheme: scheme.palette?.mode\n        };\n      } else {\n        colorSchemeStyles[`&${selector.replace(/\\s*&/, '')}`] = {\n          colorScheme: scheme.palette?.mode\n        };\n      }\n    });\n  }\n  return {\n    ...html(theme, false),\n    ...body(theme),\n    '& *, & *::before, & *::after': {\n      boxSizing: 'inherit'\n    },\n    '& strong, & b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    variants: [{\n      props: {\n        enableColorScheme: true\n      },\n      style: theme.vars ? colorSchemeStyles : {\n        colorScheme: theme.palette.mode\n      }\n    }]\n  };\n}));\nconst ScopedCssBaseline = /*#__PURE__*/React.forwardRef(function ScopedCssBaseline(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiScopedCssBaseline'\n  });\n  const {\n    className,\n    component = 'div',\n    enableColorScheme,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ScopedCssBaselineRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ScopedCssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   */\n  enableColorScheme: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ScopedCssBaseline;", "map": {"version": 3, "names": ["React", "PropTypes", "clsx", "composeClasses", "styled", "memoTheme", "useDefaultProps", "html", "body", "getScopedCssBaselineUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "slots", "root", "ScopedCssBaselineRoot", "name", "slot", "theme", "colorSchemeStyles", "colorSchemes", "Object", "entries", "for<PERSON>ach", "key", "scheme", "selector", "getColorSchemeSelector", "startsWith", "colorScheme", "palette", "mode", "replace", "boxSizing", "fontWeight", "typography", "fontWeightBold", "variants", "props", "enableColorScheme", "style", "vars", "ScopedCssBaseline", "forwardRef", "inProps", "ref", "className", "component", "other", "as", "process", "env", "NODE_ENV", "propTypes", "children", "node", "object", "string", "elementType", "bool", "sx", "oneOfType", "arrayOf", "func"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/ScopedCssBaseline/ScopedCssBaseline.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { html, body } from \"../CssBaseline/CssBaseline.js\";\nimport { getScopedCssBaselineUtilityClass } from \"./scopedCssBaselineClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getScopedCssBaselineUtilityClass, classes);\n};\nconst ScopedCssBaselineRoot = styled('div', {\n  name: 'MuiScopedCssBaseline',\n  slot: 'Root'\n})(memoTheme(({\n  theme\n}) => {\n  const colorSchemeStyles = {};\n  if (theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      const selector = theme.getColorSchemeSelector(key);\n      if (selector.startsWith('@')) {\n        colorSchemeStyles[selector] = {\n          colorScheme: scheme.palette?.mode\n        };\n      } else {\n        colorSchemeStyles[`&${selector.replace(/\\s*&/, '')}`] = {\n          colorScheme: scheme.palette?.mode\n        };\n      }\n    });\n  }\n  return {\n    ...html(theme, false),\n    ...body(theme),\n    '& *, & *::before, & *::after': {\n      boxSizing: 'inherit'\n    },\n    '& strong, & b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    variants: [{\n      props: {\n        enableColorScheme: true\n      },\n      style: theme.vars ? colorSchemeStyles : {\n        colorScheme: theme.palette.mode\n      }\n    }]\n  };\n}));\nconst ScopedCssBaseline = /*#__PURE__*/React.forwardRef(function ScopedCssBaseline(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiScopedCssBaseline'\n  });\n  const {\n    className,\n    component = 'div',\n    enableColorScheme,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ScopedCssBaselineRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ScopedCssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   */\n  enableColorScheme: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ScopedCssBaseline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,MAAM,QAAQ,yBAAyB;AAChD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,IAAI,EAAEC,IAAI,QAAQ,+BAA+B;AAC1D,SAASC,gCAAgC,QAAQ,+BAA+B;AAChF,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC;EACF,CAAC,GAAGD,UAAU;EACd,MAAME,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOb,cAAc,CAACY,KAAK,EAAEN,gCAAgC,EAAEK,OAAO,CAAC;AACzE,CAAC;AACD,MAAMG,qBAAqB,GAAGb,MAAM,CAAC,KAAK,EAAE;EAC1Cc,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE;AACR,CAAC,CAAC,CAACd,SAAS,CAAC,CAAC;EACZe;AACF,CAAC,KAAK;EACJ,MAAMC,iBAAiB,GAAG,CAAC,CAAC;EAC5B,IAAID,KAAK,CAACE,YAAY,EAAE;IACtBC,MAAM,CAACC,OAAO,CAACJ,KAAK,CAACE,YAAY,CAAC,CAACG,OAAO,CAAC,CAAC,CAACC,GAAG,EAAEC,MAAM,CAAC,KAAK;MAC5D,MAAMC,QAAQ,GAAGR,KAAK,CAACS,sBAAsB,CAACH,GAAG,CAAC;MAClD,IAAIE,QAAQ,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;QAC5BT,iBAAiB,CAACO,QAAQ,CAAC,GAAG;UAC5BG,WAAW,EAAEJ,MAAM,CAACK,OAAO,EAAEC;QAC/B,CAAC;MACH,CAAC,MAAM;QACLZ,iBAAiB,CAAC,IAAIO,QAAQ,CAACM,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG;UACtDH,WAAW,EAAEJ,MAAM,CAACK,OAAO,EAAEC;QAC/B,CAAC;MACH;IACF,CAAC,CAAC;EACJ;EACA,OAAO;IACL,GAAG1B,IAAI,CAACa,KAAK,EAAE,KAAK,CAAC;IACrB,GAAGZ,IAAI,CAACY,KAAK,CAAC;IACd,8BAA8B,EAAE;MAC9Be,SAAS,EAAE;IACb,CAAC;IACD,eAAe,EAAE;MACfC,UAAU,EAAEhB,KAAK,CAACiB,UAAU,CAACC;IAC/B,CAAC;IACDC,QAAQ,EAAE,CAAC;MACTC,KAAK,EAAE;QACLC,iBAAiB,EAAE;MACrB,CAAC;MACDC,KAAK,EAAEtB,KAAK,CAACuB,IAAI,GAAGtB,iBAAiB,GAAG;QACtCU,WAAW,EAAEX,KAAK,CAACY,OAAO,CAACC;MAC7B;IACF,CAAC;EACH,CAAC;AACH,CAAC,CAAC,CAAC;AACH,MAAMW,iBAAiB,GAAG,aAAa5C,KAAK,CAAC6C,UAAU,CAAC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/F,MAAMP,KAAK,GAAGlC,eAAe,CAAC;IAC5BkC,KAAK,EAAEM,OAAO;IACd5B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJ8B,SAAS;IACTC,SAAS,GAAG,KAAK;IACjBR,iBAAiB;IACjB,GAAGS;EACL,CAAC,GAAGV,KAAK;EACT,MAAM3B,UAAU,GAAG;IACjB,GAAG2B,KAAK;IACRS;EACF,CAAC;EACD,MAAMnC,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACM,qBAAqB,EAAE;IAC9CkC,EAAE,EAAEF,SAAS;IACbD,SAAS,EAAE9C,IAAI,CAACY,OAAO,CAACE,IAAI,EAAEgC,SAAS,CAAC;IACxCD,GAAG,EAAEA,GAAG;IACRlC,UAAU,EAAEA,UAAU;IACtB,GAAGqC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGV,iBAAiB,CAACW,SAAS,CAAC,yBAAyB;EAC3F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEvD,SAAS,CAACwD,IAAI;EACxB;AACF;AACA;EACE3C,OAAO,EAAEb,SAAS,CAACyD,MAAM;EACzB;AACF;AACA;EACEV,SAAS,EAAE/C,SAAS,CAAC0D,MAAM;EAC3B;AACF;AACA;AACA;EACEV,SAAS,EAAEhD,SAAS,CAAC2D,WAAW;EAChC;AACF;AACA;AACA;AACA;EACEnB,iBAAiB,EAAExC,SAAS,CAAC4D,IAAI;EACjC;AACF;AACA;EACEC,EAAE,EAAE7D,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAAC+D,OAAO,CAAC/D,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAACgE,IAAI,EAAEhE,SAAS,CAACyD,MAAM,EAAEzD,SAAS,CAAC4D,IAAI,CAAC,CAAC,CAAC,EAAE5D,SAAS,CAACgE,IAAI,EAAEhE,SAAS,CAACyD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAed,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}