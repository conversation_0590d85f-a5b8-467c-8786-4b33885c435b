{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Box, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Lock } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  requiredRole = null,\n  requiredPermission = null,\n  allowedRoles = null\n}) => {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        minHeight: '50vh',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        sx: {\n          mt: 2\n        },\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Redirect to login if not authenticated\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 12\n    }, this);\n  }\n\n  // Check if user has required role\n  if (requiredRole && user.role !== requiredRole) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 4,\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Lock, {\n          sx: {\n            fontSize: 60,\n            color: 'error.main',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: [\"You don't have permission to access this page. Required role: \", requiredRole]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check if user role is in allowed roles list\n  if (allowedRoles && !allowedRoles.includes(user.role)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 4,\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Lock, {\n          sx: {\n            fontSize: 60,\n            color: 'error.main',\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          children: \"Access Denied\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          color: \"text.secondary\",\n          children: [\"You don't have permission to access this page. Allowed roles: \", allowedRoles.join(', ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Check if user has required permission\n  if (requiredPermission) {\n    const hasPermission = user.role === 'admin' || user.permissions && user.permissions.includes(requiredPermission);\n    if (!hasPermission) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          sx: {\n            p: 4,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Lock, {\n            sx: {\n              fontSize: 60,\n              color: 'error.main',\n              mb: 2\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            gutterBottom: true,\n            children: \"Access Denied\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            children: [\"You don't have permission to access this feature. Required permission: \", requiredPermission]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this);\n    }\n  }\n\n  // If all checks pass, render the protected component\n  return children;\n};\n\n// Higher-order component for easier usage\n_s(ProtectedRoute, \"zPafkKLdz6KrRvMe2id3iDpNU34=\", false, function () {\n  return [useAuth, useLocation];\n});\n_c = ProtectedRoute;\nexport const withRoleProtection = (Component, options = {}) => {\n  return props => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n    ...options,\n    children: /*#__PURE__*/_jsxDEV(Component, {\n      ...props\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n\n// Specific role-based route components for common use cases\nexport const AdminRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  requiredRole: \"admin\",\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 115,\n  columnNumber: 3\n}, this);\n_c2 = AdminRoute;\nexport const StaffRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  allowedRoles: ['admin', 'staff'],\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 121,\n  columnNumber: 3\n}, this);\n_c3 = StaffRoute;\nexport const CustomerRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  allowedRoles: ['admin', 'staff', 'customer'],\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 127,\n  columnNumber: 3\n}, this);\n\n// Permission-based route components\n_c4 = CustomerRoute;\nexport const AppointmentsRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  requiredPermission: \"appointments\",\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 134,\n  columnNumber: 3\n}, this);\n_c5 = AppointmentsRoute;\nexport const CustomersRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  requiredPermission: \"customers\",\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 140,\n  columnNumber: 3\n}, this);\n_c6 = CustomersRoute;\nexport const ServicesRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  requiredPermission: \"services\",\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 146,\n  columnNumber: 3\n}, this);\n_c7 = ServicesRoute;\nexport const ReportsRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  allowedRoles: ['admin'],\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 152,\n  columnNumber: 3\n}, this);\n_c8 = ReportsRoute;\nexport const StaffManagementRoute = ({\n  children\n}) => /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n  requiredRole: \"admin\",\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 158,\n  columnNumber: 3\n}, this);\n_c9 = StaffManagementRoute;\nexport default ProtectedRoute;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"AdminRoute\");\n$RefreshReg$(_c3, \"StaffRoute\");\n$RefreshReg$(_c4, \"CustomerRoute\");\n$RefreshReg$(_c5, \"AppointmentsRoute\");\n$RefreshReg$(_c6, \"CustomersRoute\");\n$RefreshReg$(_c7, \"ServicesRoute\");\n$RefreshReg$(_c8, \"ReportsRoute\");\n$RefreshReg$(_c9, \"StaffManagementRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useAuth", "Box", "CircularProgress", "Typography", "Paper", "Lock", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "requiredRole", "requiredPermission", "allowedRoles", "_s", "user", "loading", "location", "sx", "display", "justifyContent", "alignItems", "minHeight", "flexDirection", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "mt", "to", "state", "from", "replace", "role", "p", "textAlign", "fontSize", "color", "mb", "gutterBottom", "includes", "join", "hasPermission", "permissions", "_c", "withRoleProtection", "Component", "options", "props", "AdminRoute", "_c2", "StaffRoute", "_c3", "CustomerRoute", "_c4", "AppointmentsRoute", "_c5", "CustomersRoute", "_c6", "ServicesRoute", "_c7", "ReportsRoute", "_c8", "StaffManagementRoute", "_c9", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Box, CircularProgress, Typography, Paper } from '@mui/material';\nimport { Lock } from '@mui/icons-material';\n\nconst ProtectedRoute = ({ \n  children, \n  requiredRole = null, \n  requiredPermission = null,\n  allowedRoles = null \n}) => {\n  const { user, loading } = useAuth();\n  const location = useLocation();\n\n  // Show loading spinner while checking authentication\n  if (loading) {\n    return (\n      <Box\n        sx={{\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '50vh',\n          flexDirection: 'column'\n        }}\n      >\n        <CircularProgress size={60} />\n        <Typography variant=\"h6\" sx={{ mt: 2 }}>\n          Loading...\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Redirect to login if not authenticated\n  if (!user) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Check if user has required role\n  if (requiredRole && user.role !== requiredRole) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Paper sx={{ p: 4, textAlign: 'center' }}>\n          <Lock sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />\n          <Typography variant=\"h5\" gutterBottom>\n            Access Denied\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            You don't have permission to access this page.\n            Required role: {requiredRole}\n          </Typography>\n        </Paper>\n      </Box>\n    );\n  }\n\n  // Check if user role is in allowed roles list\n  if (allowedRoles && !allowedRoles.includes(user.role)) {\n    return (\n      <Box sx={{ p: 3 }}>\n        <Paper sx={{ p: 4, textAlign: 'center' }}>\n          <Lock sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />\n          <Typography variant=\"h5\" gutterBottom>\n            Access Denied\n          </Typography>\n          <Typography variant=\"body1\" color=\"text.secondary\">\n            You don't have permission to access this page.\n            Allowed roles: {allowedRoles.join(', ')}\n          </Typography>\n        </Paper>\n      </Box>\n    );\n  }\n\n  // Check if user has required permission\n  if (requiredPermission) {\n    const hasPermission = user.role === 'admin' || \n                         (user.permissions && user.permissions.includes(requiredPermission));\n    \n    if (!hasPermission) {\n      return (\n        <Box sx={{ p: 3 }}>\n          <Paper sx={{ p: 4, textAlign: 'center' }}>\n            <Lock sx={{ fontSize: 60, color: 'error.main', mb: 2 }} />\n            <Typography variant=\"h5\" gutterBottom>\n              Access Denied\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\">\n              You don't have permission to access this feature.\n              Required permission: {requiredPermission}\n            </Typography>\n          </Paper>\n        </Box>\n      );\n    }\n  }\n\n  // If all checks pass, render the protected component\n  return children;\n};\n\n// Higher-order component for easier usage\nexport const withRoleProtection = (Component, options = {}) => {\n  return (props) => (\n    <ProtectedRoute {...options}>\n      <Component {...props} />\n    </ProtectedRoute>\n  );\n};\n\n// Specific role-based route components for common use cases\nexport const AdminRoute = ({ children }) => (\n  <ProtectedRoute requiredRole=\"admin\">\n    {children}\n  </ProtectedRoute>\n);\n\nexport const StaffRoute = ({ children }) => (\n  <ProtectedRoute allowedRoles={['admin', 'staff']}>\n    {children}\n  </ProtectedRoute>\n);\n\nexport const CustomerRoute = ({ children }) => (\n  <ProtectedRoute allowedRoles={['admin', 'staff', 'customer']}>\n    {children}\n  </ProtectedRoute>\n);\n\n// Permission-based route components\nexport const AppointmentsRoute = ({ children }) => (\n  <ProtectedRoute requiredPermission=\"appointments\">\n    {children}\n  </ProtectedRoute>\n);\n\nexport const CustomersRoute = ({ children }) => (\n  <ProtectedRoute requiredPermission=\"customers\">\n    {children}\n  </ProtectedRoute>\n);\n\nexport const ServicesRoute = ({ children }) => (\n  <ProtectedRoute requiredPermission=\"services\">\n    {children}\n  </ProtectedRoute>\n);\n\nexport const ReportsRoute = ({ children }) => (\n  <ProtectedRoute allowedRoles={['admin']}>\n    {children}\n  </ProtectedRoute>\n);\n\nexport const StaffManagementRoute = ({ children }) => (\n  <ProtectedRoute requiredRole=\"admin\">\n    {children}\n  </ProtectedRoute>\n);\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,GAAG,EAAEC,gBAAgB,EAAEC,UAAU,EAAEC,KAAK,QAAQ,eAAe;AACxE,SAASC,IAAI,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,cAAc,GAAGA,CAAC;EACtBC,QAAQ;EACRC,YAAY,GAAG,IAAI;EACnBC,kBAAkB,GAAG,IAAI;EACzBC,YAAY,GAAG;AACjB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGf,OAAO,CAAC,CAAC;EACnC,MAAMgB,QAAQ,GAAGjB,WAAW,CAAC,CAAC;;EAE9B;EACA,IAAIgB,OAAO,EAAE;IACX,oBACER,OAAA,CAACN,GAAG;MACFgB,EAAE,EAAE;QACFC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,MAAM;QACjBC,aAAa,EAAE;MACjB,CAAE;MAAAb,QAAA,gBAEFF,OAAA,CAACL,gBAAgB;QAACqB,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9BpB,OAAA,CAACJ,UAAU;QAACyB,OAAO,EAAC,IAAI;QAACX,EAAE,EAAE;UAAEY,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,EAAC;MAExC;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;;EAEA;EACA,IAAI,CAACb,IAAI,EAAE;IACT,oBAAOP,OAAA,CAACT,QAAQ;MAACgC,EAAE,EAAC,QAAQ;MAACC,KAAK,EAAE;QAAEC,IAAI,EAAEhB;MAAS,CAAE;MAACiB,OAAO;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;;EAEA;EACA,IAAIjB,YAAY,IAAII,IAAI,CAACoB,IAAI,KAAKxB,YAAY,EAAE;IAC9C,oBACEH,OAAA,CAACN,GAAG;MAACgB,EAAE,EAAE;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAA1B,QAAA,eAChBF,OAAA,CAACH,KAAK;QAACa,EAAE,EAAE;UAAEkB,CAAC,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBACvCF,OAAA,CAACF,IAAI;UAACY,EAAE,EAAE;YAAEoB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,YAAY;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DpB,OAAA,CAACJ,UAAU;UAACyB,OAAO,EAAC,IAAI;UAACY,YAAY;UAAA/B,QAAA,EAAC;QAEtC;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpB,OAAA,CAACJ,UAAU;UAACyB,OAAO,EAAC,OAAO;UAACU,KAAK,EAAC,gBAAgB;UAAA7B,QAAA,GAAC,gEAElC,EAACC,YAAY;QAAA;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;;EAEA;EACA,IAAIf,YAAY,IAAI,CAACA,YAAY,CAAC6B,QAAQ,CAAC3B,IAAI,CAACoB,IAAI,CAAC,EAAE;IACrD,oBACE3B,OAAA,CAACN,GAAG;MAACgB,EAAE,EAAE;QAAEkB,CAAC,EAAE;MAAE,CAAE;MAAA1B,QAAA,eAChBF,OAAA,CAACH,KAAK;QAACa,EAAE,EAAE;UAAEkB,CAAC,EAAE,CAAC;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAA3B,QAAA,gBACvCF,OAAA,CAACF,IAAI;UAACY,EAAE,EAAE;YAAEoB,QAAQ,EAAE,EAAE;YAAEC,KAAK,EAAE,YAAY;YAAEC,EAAE,EAAE;UAAE;QAAE;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC1DpB,OAAA,CAACJ,UAAU;UAACyB,OAAO,EAAC,IAAI;UAACY,YAAY;UAAA/B,QAAA,EAAC;QAEtC;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpB,OAAA,CAACJ,UAAU;UAACyB,OAAO,EAAC,OAAO;UAACU,KAAK,EAAC,gBAAgB;UAAA7B,QAAA,GAAC,gEAElC,EAACG,YAAY,CAAC8B,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV;;EAEA;EACA,IAAIhB,kBAAkB,EAAE;IACtB,MAAMgC,aAAa,GAAG7B,IAAI,CAACoB,IAAI,KAAK,OAAO,IACrBpB,IAAI,CAAC8B,WAAW,IAAI9B,IAAI,CAAC8B,WAAW,CAACH,QAAQ,CAAC9B,kBAAkB,CAAE;IAExF,IAAI,CAACgC,aAAa,EAAE;MAClB,oBACEpC,OAAA,CAACN,GAAG;QAACgB,EAAE,EAAE;UAAEkB,CAAC,EAAE;QAAE,CAAE;QAAA1B,QAAA,eAChBF,OAAA,CAACH,KAAK;UAACa,EAAE,EAAE;YAAEkB,CAAC,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAA3B,QAAA,gBACvCF,OAAA,CAACF,IAAI;YAACY,EAAE,EAAE;cAAEoB,QAAQ,EAAE,EAAE;cAAEC,KAAK,EAAE,YAAY;cAAEC,EAAE,EAAE;YAAE;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1DpB,OAAA,CAACJ,UAAU;YAACyB,OAAO,EAAC,IAAI;YAACY,YAAY;YAAA/B,QAAA,EAAC;UAEtC;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpB,OAAA,CAACJ,UAAU;YAACyB,OAAO,EAAC,OAAO;YAACU,KAAK,EAAC,gBAAgB;YAAA7B,QAAA,GAAC,yEAE5B,EAACE,kBAAkB;UAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAEV;EACF;;EAEA;EACA,OAAOlB,QAAQ;AACjB,CAAC;;AAED;AAAAI,EAAA,CAjGML,cAAc;EAAA,QAMQR,OAAO,EAChBD,WAAW;AAAA;AAAA8C,EAAA,GAPxBrC,cAAc;AAkGpB,OAAO,MAAMsC,kBAAkB,GAAGA,CAACC,SAAS,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EAC7D,OAAQC,KAAK,iBACX1C,OAAA,CAACC,cAAc;IAAA,GAAKwC,OAAO;IAAAvC,QAAA,eACzBF,OAAA,CAACwC,SAAS;MAAA,GAAKE;IAAK;MAAAzB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACjB;AACH,CAAC;;AAED;AACA,OAAO,MAAMuB,UAAU,GAAGA,CAAC;EAAEzC;AAAS,CAAC,kBACrCF,OAAA,CAACC,cAAc;EAACE,YAAY,EAAC,OAAO;EAAAD,QAAA,EACjCA;AAAQ;EAAAe,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;AAACwB,GAAA,GAJWD,UAAU;AAMvB,OAAO,MAAME,UAAU,GAAGA,CAAC;EAAE3C;AAAS,CAAC,kBACrCF,OAAA,CAACC,cAAc;EAACI,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,CAAE;EAAAH,QAAA,EAC9CA;AAAQ;EAAAe,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;AAAC0B,GAAA,GAJWD,UAAU;AAMvB,OAAO,MAAME,aAAa,GAAGA,CAAC;EAAE7C;AAAS,CAAC,kBACxCF,OAAA,CAACC,cAAc;EAACI,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,UAAU,CAAE;EAAAH,QAAA,EAC1DA;AAAQ;EAAAe,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;;AAED;AAAA4B,GAAA,GANaD,aAAa;AAO1B,OAAO,MAAME,iBAAiB,GAAGA,CAAC;EAAE/C;AAAS,CAAC,kBAC5CF,OAAA,CAACC,cAAc;EAACG,kBAAkB,EAAC,cAAc;EAAAF,QAAA,EAC9CA;AAAQ;EAAAe,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;AAAC8B,GAAA,GAJWD,iBAAiB;AAM9B,OAAO,MAAME,cAAc,GAAGA,CAAC;EAAEjD;AAAS,CAAC,kBACzCF,OAAA,CAACC,cAAc;EAACG,kBAAkB,EAAC,WAAW;EAAAF,QAAA,EAC3CA;AAAQ;EAAAe,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;AAACgC,GAAA,GAJWD,cAAc;AAM3B,OAAO,MAAME,aAAa,GAAGA,CAAC;EAAEnD;AAAS,CAAC,kBACxCF,OAAA,CAACC,cAAc;EAACG,kBAAkB,EAAC,UAAU;EAAAF,QAAA,EAC1CA;AAAQ;EAAAe,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;AAACkC,GAAA,GAJWD,aAAa;AAM1B,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAErD;AAAS,CAAC,kBACvCF,OAAA,CAACC,cAAc;EAACI,YAAY,EAAE,CAAC,OAAO,CAAE;EAAAH,QAAA,EACrCA;AAAQ;EAAAe,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;AAACoC,GAAA,GAJWD,YAAY;AAMzB,OAAO,MAAME,oBAAoB,GAAGA,CAAC;EAAEvD;AAAS,CAAC,kBAC/CF,OAAA,CAACC,cAAc;EAACE,YAAY,EAAC,OAAO;EAAAD,QAAA,EACjCA;AAAQ;EAAAe,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACjB;AAACsC,GAAA,GAJWD,oBAAoB;AAMjC,eAAexD,cAAc;AAAC,IAAAqC,EAAA,EAAAM,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAArB,EAAA;AAAAqB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAb,GAAA;AAAAa,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAT,GAAA;AAAAS,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}