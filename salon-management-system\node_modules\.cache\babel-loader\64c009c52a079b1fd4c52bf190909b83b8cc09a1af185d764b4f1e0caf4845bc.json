{"ast": null, "code": "import generateUtilityClass from \"../generateUtilityClass/index.js\";\nexport default function generateUtilityClasses(componentName, slots, globalStatePrefix = 'Mui') {\n  const result = {};\n  slots.forEach(slot => {\n    result[slot] = generateUtilityClass(componentName, slot, globalStatePrefix);\n  });\n  return result;\n}", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "componentName", "slots", "globalStatePrefix", "result", "for<PERSON>ach", "slot"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/utils/esm/generateUtilityClasses/generateUtilityClasses.js"], "sourcesContent": ["import generateUtilityClass from \"../generateUtilityClass/index.js\";\nexport default function generateUtilityClasses(componentName, slots, globalStatePrefix = 'Mui') {\n  const result = {};\n  slots.forEach(slot => {\n    result[slot] = generateUtilityClass(componentName, slot, globalStatePrefix);\n  });\n  return result;\n}"], "mappings": "AAAA,OAAOA,oBAAoB,MAAM,kCAAkC;AACnE,eAAe,SAASC,sBAAsBA,CAACC,aAAa,EAAEC,KAAK,EAAEC,iBAAiB,GAAG,KAAK,EAAE;EAC9F,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBF,KAAK,CAACG,OAAO,CAACC,IAAI,IAAI;IACpBF,MAAM,CAACE,IAAI,CAAC,GAAGP,oBAAoB,CAACE,aAAa,EAAEK,IAAI,EAAEH,iBAAiB,CAAC;EAC7E,CAAC,CAAC;EACF,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}