{"ast": null, "code": "import { useEffect, useRef } from 'react';\n\n/**\n * Creates a `Ref` whose value is updated in an effect, ensuring the most recent\n * value is the one rendered with. Generally only required for Concurrent mode usage\n * where previous work in `render()` may be discarded before being used.\n *\n * This is safe to access in an event handler.\n *\n * @param value The `Ref` value\n */\nfunction useCommittedRef(value) {\n  const ref = useRef(value);\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n  return ref;\n}\nexport default useCommittedRef;", "map": {"version": 3, "names": ["useEffect", "useRef", "useCommittedRef", "value", "ref", "current"], "sources": ["D:/Project/salon-management-system/node_modules/@restart/hooks/esm/useCommittedRef.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\n\n/**\n * Creates a `Ref` whose value is updated in an effect, ensuring the most recent\n * value is the one rendered with. Generally only required for Concurrent mode usage\n * where previous work in `render()` may be discarded before being used.\n *\n * This is safe to access in an event handler.\n *\n * @param value The `Ref` value\n */\nfunction useCommittedRef(value) {\n  const ref = useRef(value);\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n  return ref;\n}\nexport default useCommittedRef;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,MAAMC,GAAG,GAAGH,MAAM,CAACE,KAAK,CAAC;EACzBH,SAAS,CAAC,MAAM;IACdI,GAAG,CAACC,OAAO,GAAGF,KAAK;EACrB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,OAAOC,GAAG;AACZ;AACA,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}