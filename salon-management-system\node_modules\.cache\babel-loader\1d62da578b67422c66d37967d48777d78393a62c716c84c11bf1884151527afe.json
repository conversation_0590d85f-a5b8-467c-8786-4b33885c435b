{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\components\\\\CalendarView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport { Calendar, momentLocalizer } from 'react-big-calendar';\nimport moment from 'moment';\nimport { Box, Typography, Paper, Chip, Dialog, DialogTitle, DialogContent, DialogActions, Button, Grid, Card, CardContent, IconButton, Tooltip } from '@mui/material';\nimport { Event as EventIcon, Person as PersonIcon, Schedule as ScheduleIcon, AttachMoney as MoneyIcon, Close as CloseIcon } from '@mui/icons-material';\nimport { useBooking } from '../contexts/BookingContext';\nimport 'react-big-calendar/lib/css/react-big-calendar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst localizer = momentLocalizer(moment);\nconst CalendarView = ({\n  onDateSelect,\n  onAppointmentSelect\n}) => {\n  _s();\n  const {\n    appointments\n  } = useBooking();\n  const [selectedEvent, setSelectedEvent] = useState(null);\n  const [eventDialogOpen, setEventDialogOpen] = useState(false);\n  const [currentDate, setCurrentDate] = useState(new Date());\n  const [currentView, setCurrentView] = useState('week');\n\n  // Convert appointments to calendar events\n  const events = useMemo(() => {\n    return appointments.map(appointment => {\n      const startDate = new Date(`${appointment.date}T${appointment.time}`);\n      const endDate = new Date(startDate.getTime() + appointment.duration * 60000);\n      return {\n        id: appointment.id,\n        title: `${appointment.customer} - ${appointment.service}`,\n        start: startDate,\n        end: endDate,\n        resource: appointment\n      };\n    });\n  }, [appointments]);\n\n  // Custom event style based on status\n  const eventStyleGetter = event => {\n    const appointment = event.resource;\n    let backgroundColor = '#3174ad';\n    switch (appointment.status) {\n      case 'completed':\n        backgroundColor = '#4caf50';\n        break;\n      case 'in-progress':\n        backgroundColor = '#ff9800';\n        break;\n      case 'scheduled':\n        backgroundColor = '#2196f3';\n        break;\n      case 'cancelled':\n        backgroundColor = '#f44336';\n        break;\n      default:\n        backgroundColor = '#9e9e9e';\n    }\n    return {\n      style: {\n        backgroundColor,\n        borderRadius: '4px',\n        opacity: 0.8,\n        color: 'white',\n        border: '0px',\n        display: 'block'\n      }\n    };\n  };\n\n  // Handle event selection\n  const handleSelectEvent = event => {\n    setSelectedEvent(event.resource);\n    setEventDialogOpen(true);\n    if (onAppointmentSelect) {\n      onAppointmentSelect(event.resource);\n    }\n  };\n\n  // Handle slot selection (for booking)\n  const handleSelectSlot = slotInfo => {\n    if (onDateSelect) {\n      onDateSelect(slotInfo.start);\n    }\n  };\n\n  // Get status color\n  const getStatusColor = status => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'in-progress':\n        return 'warning';\n      case 'scheduled':\n        return 'primary';\n      case 'cancelled':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  // Handle navigation\n  const handleNavigate = action => {\n    let newDate = new Date(currentDate);\n    switch (action) {\n      case 'PREV':\n        if (currentView === 'month') {\n          newDate.setMonth(newDate.getMonth() - 1);\n        } else if (currentView === 'week') {\n          newDate.setDate(newDate.getDate() - 7);\n        } else if (currentView === 'day') {\n          newDate.setDate(newDate.getDate() - 1);\n        }\n        break;\n      case 'NEXT':\n        if (currentView === 'month') {\n          newDate.setMonth(newDate.getMonth() + 1);\n        } else if (currentView === 'week') {\n          newDate.setDate(newDate.getDate() + 7);\n        } else if (currentView === 'day') {\n          newDate.setDate(newDate.getDate() + 1);\n        }\n        break;\n      case 'TODAY':\n        newDate = new Date();\n        break;\n      default:\n        return;\n    }\n    setCurrentDate(newDate);\n  };\n\n  // Handle view change\n  const handleViewChange = view => {\n    setCurrentView(view);\n  };\n\n  // Custom toolbar\n  const CustomToolbar = ({\n    label\n  }) => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      mb: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => handleNavigate('PREV'),\n        variant: \"outlined\",\n        size: \"small\",\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => handleNavigate('TODAY'),\n        variant: \"outlined\",\n        size: \"small\",\n        children: \"Today\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => handleNavigate('NEXT'),\n        variant: \"outlined\",\n        size: \"small\",\n        children: \"Next\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      component: \"div\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        gap: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => handleViewChange('month'),\n        variant: currentView === 'month' ? 'contained' : 'outlined',\n        size: \"small\",\n        children: \"Month\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => handleViewChange('week'),\n        variant: currentView === 'week' ? 'contained' : 'outlined',\n        size: \"small\",\n        children: \"Week\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => handleViewChange('day'),\n        variant: currentView === 'day' ? 'contained' : 'outlined',\n        size: \"small\",\n        children: \"Day\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '600px',\n      p: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        height: '100%'\n      },\n      children: /*#__PURE__*/_jsxDEV(Calendar, {\n        localizer: localizer,\n        events: events,\n        startAccessor: \"start\",\n        endAccessor: \"end\",\n        style: {\n          height: '100%'\n        },\n        onSelectEvent: handleSelectEvent,\n        onSelectSlot: handleSelectSlot,\n        selectable: true,\n        eventPropGetter: eventStyleGetter,\n        components: {\n          toolbar: CustomToolbar\n        },\n        views: ['month', 'week', 'day'],\n        defaultView: \"week\",\n        step: 30,\n        timeslots: 2,\n        min: new Date(2024, 0, 1, 8, 0) // 8 AM\n        ,\n        max: new Date(2024, 0, 1, 20, 0) // 8 PM\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: eventDialogOpen,\n      onClose: () => setEventDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          children: \"Appointment Details\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => setEventDialogOpen(false),\n          children: /*#__PURE__*/_jsxDEV(CloseIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), selectedEvent && /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mb: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                    sx: {\n                      mr: 1,\n                      color: 'primary.main'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    children: selectedEvent.customer\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                    label: selectedEvent.status,\n                    color: getStatusColor(selectedEvent.status),\n                    size: \"small\",\n                    sx: {\n                      ml: 'auto'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                  container: true,\n                  spacing: 2,\n                  children: [/*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(EventIcon, {\n                        sx: {\n                          mr: 1,\n                          color: 'text.secondary'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Service\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 264,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: selectedEvent.service\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 261,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(PersonIcon, {\n                        sx: {\n                          mr: 1,\n                          color: 'text.secondary'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 273,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Stylist\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 274,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: selectedEvent.stylist\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(ScheduleIcon, {\n                        sx: {\n                          mr: 1,\n                          color: 'text.secondary'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 283,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Date & Time\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 284,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: [selectedEvent.date, \" at \", selectedEvent.time]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 288,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: [\"Duration: \", selectedEvent.duration, \" minutes\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 291,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    sm: 6,\n                    children: [/*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        mb: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(MoneyIcon, {\n                        sx: {\n                          mr: 1,\n                          color: 'text.secondary'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        color: \"text.secondary\",\n                        children: \"Price\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body1\",\n                      children: [\"$\", selectedEvent.price]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 296,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                    item: true,\n                    xs: 12,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: [\"Phone: \", selectedEvent.phone]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 307,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 260,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setEventDialogOpen(false),\n          children: \"Close\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n};\n_s(CalendarView, \"1RLYOG359iQ5otgjx9k+8coBBG0=\", false, function () {\n  return [useBooking];\n});\n_c = CalendarView;\nexport default CalendarView;\nvar _c;\n$RefreshReg$(_c, \"CalendarView\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "Calendar", "momentLocalizer", "moment", "Box", "Typography", "Paper", "Chip", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "Event", "EventIcon", "Person", "PersonIcon", "Schedule", "ScheduleIcon", "AttachMoney", "MoneyIcon", "Close", "CloseIcon", "useBooking", "jsxDEV", "_jsxDEV", "localizer", "CalendarView", "onDateSelect", "onAppointmentSelect", "_s", "appointments", "selectedEvent", "setSelectedEvent", "eventDialogOpen", "setEventDialogOpen", "currentDate", "setCurrentDate", "Date", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "events", "map", "appointment", "startDate", "date", "time", "endDate", "getTime", "duration", "id", "title", "customer", "service", "start", "end", "resource", "eventStyleGetter", "event", "backgroundColor", "status", "style", "borderRadius", "opacity", "color", "border", "display", "handleSelectEvent", "handleSelectSlot", "slotInfo", "getStatusColor", "handleNavigate", "action", "newDate", "setMonth", "getMonth", "setDate", "getDate", "handleViewChange", "view", "CustomToolbar", "label", "sx", "justifyContent", "alignItems", "mb", "children", "gap", "onClick", "variant", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "height", "p", "startAccessor", "endAccessor", "onSelectEvent", "onSelectSlot", "selectable", "eventPropGetter", "components", "toolbar", "views", "defaultView", "step", "timeslots", "min", "max", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "container", "spacing", "item", "xs", "mr", "ml", "sm", "stylist", "price", "phone", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/components/CalendarView.js"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport { Calendar, momentLocalizer } from 'react-big-calendar';\nimport moment from 'moment';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Grid,\n  Card,\n  CardContent,\n  IconButton,\n  Tooltip,\n} from '@mui/material';\nimport {\n  Event as EventIcon,\n  Person as PersonIcon,\n  Schedule as ScheduleIcon,\n  AttachMoney as MoneyIcon,\n  Close as CloseIcon,\n} from '@mui/icons-material';\nimport { useBooking } from '../contexts/BookingContext';\nimport 'react-big-calendar/lib/css/react-big-calendar.css';\n\nconst localizer = momentLocalizer(moment);\n\nconst CalendarView = ({ onDateSelect, onAppointmentSelect }) => {\n  const { appointments } = useBooking();\n  const [selectedEvent, setSelectedEvent] = useState(null);\n  const [eventDialogOpen, setEventDialogOpen] = useState(false);\n  const [currentDate, setCurrentDate] = useState(new Date());\n  const [currentView, setCurrentView] = useState('week');\n\n  // Convert appointments to calendar events\n  const events = useMemo(() => {\n    return appointments.map(appointment => {\n      const startDate = new Date(`${appointment.date}T${appointment.time}`);\n      const endDate = new Date(startDate.getTime() + appointment.duration * 60000);\n      \n      return {\n        id: appointment.id,\n        title: `${appointment.customer} - ${appointment.service}`,\n        start: startDate,\n        end: endDate,\n        resource: appointment,\n      };\n    });\n  }, [appointments]);\n\n  // Custom event style based on status\n  const eventStyleGetter = (event) => {\n    const appointment = event.resource;\n    let backgroundColor = '#3174ad';\n    \n    switch (appointment.status) {\n      case 'completed':\n        backgroundColor = '#4caf50';\n        break;\n      case 'in-progress':\n        backgroundColor = '#ff9800';\n        break;\n      case 'scheduled':\n        backgroundColor = '#2196f3';\n        break;\n      case 'cancelled':\n        backgroundColor = '#f44336';\n        break;\n      default:\n        backgroundColor = '#9e9e9e';\n    }\n\n    return {\n      style: {\n        backgroundColor,\n        borderRadius: '4px',\n        opacity: 0.8,\n        color: 'white',\n        border: '0px',\n        display: 'block',\n      },\n    };\n  };\n\n  // Handle event selection\n  const handleSelectEvent = (event) => {\n    setSelectedEvent(event.resource);\n    setEventDialogOpen(true);\n    if (onAppointmentSelect) {\n      onAppointmentSelect(event.resource);\n    }\n  };\n\n  // Handle slot selection (for booking)\n  const handleSelectSlot = (slotInfo) => {\n    if (onDateSelect) {\n      onDateSelect(slotInfo.start);\n    }\n  };\n\n  // Get status color\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed':\n        return 'success';\n      case 'in-progress':\n        return 'warning';\n      case 'scheduled':\n        return 'primary';\n      case 'cancelled':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n\n  // Handle navigation\n  const handleNavigate = (action) => {\n    let newDate = new Date(currentDate);\n\n    switch (action) {\n      case 'PREV':\n        if (currentView === 'month') {\n          newDate.setMonth(newDate.getMonth() - 1);\n        } else if (currentView === 'week') {\n          newDate.setDate(newDate.getDate() - 7);\n        } else if (currentView === 'day') {\n          newDate.setDate(newDate.getDate() - 1);\n        }\n        break;\n      case 'NEXT':\n        if (currentView === 'month') {\n          newDate.setMonth(newDate.getMonth() + 1);\n        } else if (currentView === 'week') {\n          newDate.setDate(newDate.getDate() + 7);\n        } else if (currentView === 'day') {\n          newDate.setDate(newDate.getDate() + 1);\n        }\n        break;\n      case 'TODAY':\n        newDate = new Date();\n        break;\n      default:\n        return;\n    }\n\n    setCurrentDate(newDate);\n  };\n\n  // Handle view change\n  const handleViewChange = (view) => {\n    setCurrentView(view);\n  };\n\n  // Custom toolbar\n  const CustomToolbar = ({ label }) => (\n    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n      <Box sx={{ display: 'flex', gap: 1 }}>\n        <Button onClick={() => handleNavigate('PREV')} variant=\"outlined\" size=\"small\">\n          Previous\n        </Button>\n        <Button onClick={() => handleNavigate('TODAY')} variant=\"outlined\" size=\"small\">\n          Today\n        </Button>\n        <Button onClick={() => handleNavigate('NEXT')} variant=\"outlined\" size=\"small\">\n          Next\n        </Button>\n      </Box>\n\n      <Typography variant=\"h6\" component=\"div\">\n        {label}\n      </Typography>\n\n      <Box sx={{ display: 'flex', gap: 1 }}>\n        <Button\n          onClick={() => handleViewChange('month')}\n          variant={currentView === 'month' ? 'contained' : 'outlined'}\n          size=\"small\"\n        >\n          Month\n        </Button>\n        <Button\n          onClick={() => handleViewChange('week')}\n          variant={currentView === 'week' ? 'contained' : 'outlined'}\n          size=\"small\"\n        >\n          Week\n        </Button>\n        <Button\n          onClick={() => handleViewChange('day')}\n          variant={currentView === 'day' ? 'contained' : 'outlined'}\n          size=\"small\"\n        >\n          Day\n        </Button>\n      </Box>\n    </Box>\n  );\n\n  return (\n    <Box sx={{ height: '600px', p: 2 }}>\n      <Paper sx={{ p: 2, height: '100%' }}>\n        <Calendar\n          localizer={localizer}\n          events={events}\n          startAccessor=\"start\"\n          endAccessor=\"end\"\n          style={{ height: '100%' }}\n          onSelectEvent={handleSelectEvent}\n          onSelectSlot={handleSelectSlot}\n          selectable\n          eventPropGetter={eventStyleGetter}\n          components={{\n            toolbar: CustomToolbar,\n          }}\n          views={['month', 'week', 'day']}\n          defaultView=\"week\"\n          step={30}\n          timeslots={2}\n          min={new Date(2024, 0, 1, 8, 0)} // 8 AM\n          max={new Date(2024, 0, 1, 20, 0)} // 8 PM\n        />\n      </Paper>\n\n      {/* Appointment Details Dialog */}\n      <Dialog\n        open={eventDialogOpen}\n        onClose={() => setEventDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <Typography variant=\"h6\">Appointment Details</Typography>\n          <IconButton onClick={() => setEventDialogOpen(false)}>\n            <CloseIcon />\n          </IconButton>\n        </DialogTitle>\n        \n        {selectedEvent && (\n          <DialogContent>\n            <Grid container spacing={2}>\n              <Grid item xs={12}>\n                <Card variant=\"outlined\">\n                  <CardContent>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                      <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />\n                      <Typography variant=\"h6\">{selectedEvent.customer}</Typography>\n                      <Chip\n                        label={selectedEvent.status}\n                        color={getStatusColor(selectedEvent.status)}\n                        size=\"small\"\n                        sx={{ ml: 'auto' }}\n                      />\n                    </Box>\n                    \n                    <Grid container spacing={2}>\n                      <Grid item xs={12} sm={6}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                          <EventIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Service\n                          </Typography>\n                        </Box>\n                        <Typography variant=\"body1\">{selectedEvent.service}</Typography>\n                      </Grid>\n                      \n                      <Grid item xs={12} sm={6}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                          <PersonIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Stylist\n                          </Typography>\n                        </Box>\n                        <Typography variant=\"body1\">{selectedEvent.stylist}</Typography>\n                      </Grid>\n                      \n                      <Grid item xs={12} sm={6}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                          <ScheduleIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Date & Time\n                          </Typography>\n                        </Box>\n                        <Typography variant=\"body1\">\n                          {selectedEvent.date} at {selectedEvent.time}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Duration: {selectedEvent.duration} minutes\n                        </Typography>\n                      </Grid>\n                      \n                      <Grid item xs={12} sm={6}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                          <MoneyIcon sx={{ mr: 1, color: 'text.secondary' }} />\n                          <Typography variant=\"body2\" color=\"text.secondary\">\n                            Price\n                          </Typography>\n                        </Box>\n                        <Typography variant=\"body1\">${selectedEvent.price}</Typography>\n                      </Grid>\n                      \n                      <Grid item xs={12}>\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Phone: {selectedEvent.phone}\n                        </Typography>\n                      </Grid>\n                    </Grid>\n                  </CardContent>\n                </Card>\n              </Grid>\n            </Grid>\n          </DialogContent>\n        )}\n        \n        <DialogActions>\n          <Button onClick={() => setEventDialogOpen(false)}>Close</Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default CalendarView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,SAASC,QAAQ,EAAEC,eAAe,QAAQ,oBAAoB;AAC9D,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,QAAQ,IAAIC,YAAY,EACxBC,WAAW,IAAIC,SAAS,EACxBC,KAAK,IAAIC,SAAS,QACb,qBAAqB;AAC5B,SAASC,UAAU,QAAQ,4BAA4B;AACvD,OAAO,mDAAmD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,MAAMC,SAAS,GAAG7B,eAAe,CAACC,MAAM,CAAC;AAEzC,MAAM6B,YAAY,GAAGA,CAAC;EAAEC,YAAY;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EAC9D,MAAM;IAAEC;EAAa,CAAC,GAAGR,UAAU,CAAC,CAAC;EACrC,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACwC,eAAe,EAAEC,kBAAkB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC,IAAI4C,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC,MAAM,CAAC;;EAEtD;EACA,MAAM+C,MAAM,GAAG9C,OAAO,CAAC,MAAM;IAC3B,OAAOoC,YAAY,CAACW,GAAG,CAACC,WAAW,IAAI;MACrC,MAAMC,SAAS,GAAG,IAAIN,IAAI,CAAC,GAAGK,WAAW,CAACE,IAAI,IAAIF,WAAW,CAACG,IAAI,EAAE,CAAC;MACrE,MAAMC,OAAO,GAAG,IAAIT,IAAI,CAACM,SAAS,CAACI,OAAO,CAAC,CAAC,GAAGL,WAAW,CAACM,QAAQ,GAAG,KAAK,CAAC;MAE5E,OAAO;QACLC,EAAE,EAAEP,WAAW,CAACO,EAAE;QAClBC,KAAK,EAAE,GAAGR,WAAW,CAACS,QAAQ,MAAMT,WAAW,CAACU,OAAO,EAAE;QACzDC,KAAK,EAAEV,SAAS;QAChBW,GAAG,EAAER,OAAO;QACZS,QAAQ,EAAEb;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACZ,YAAY,CAAC,CAAC;;EAElB;EACA,MAAM0B,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMf,WAAW,GAAGe,KAAK,CAACF,QAAQ;IAClC,IAAIG,eAAe,GAAG,SAAS;IAE/B,QAAQhB,WAAW,CAACiB,MAAM;MACxB,KAAK,WAAW;QACdD,eAAe,GAAG,SAAS;QAC3B;MACF,KAAK,aAAa;QAChBA,eAAe,GAAG,SAAS;QAC3B;MACF,KAAK,WAAW;QACdA,eAAe,GAAG,SAAS;QAC3B;MACF,KAAK,WAAW;QACdA,eAAe,GAAG,SAAS;QAC3B;MACF;QACEA,eAAe,GAAG,SAAS;IAC/B;IAEA,OAAO;MACLE,KAAK,EAAE;QACLF,eAAe;QACfG,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,GAAG;QACZC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;MACX;IACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMC,iBAAiB,GAAIT,KAAK,IAAK;IACnCzB,gBAAgB,CAACyB,KAAK,CAACF,QAAQ,CAAC;IAChCrB,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAIN,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC6B,KAAK,CAACF,QAAQ,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,IAAIzC,YAAY,EAAE;MAChBA,YAAY,CAACyC,QAAQ,CAACf,KAAK,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMgB,cAAc,GAAIV,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,aAAa;QAChB,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,OAAO;MAChB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;;EAED;EACA,MAAMW,cAAc,GAAIC,MAAM,IAAK;IACjC,IAAIC,OAAO,GAAG,IAAInC,IAAI,CAACF,WAAW,CAAC;IAEnC,QAAQoC,MAAM;MACZ,KAAK,MAAM;QACT,IAAIjC,WAAW,KAAK,OAAO,EAAE;UAC3BkC,OAAO,CAACC,QAAQ,CAACD,OAAO,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC,MAAM,IAAIpC,WAAW,KAAK,MAAM,EAAE;UACjCkC,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAItC,WAAW,KAAK,KAAK,EAAE;UAChCkC,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACxC;QACA;MACF,KAAK,MAAM;QACT,IAAItC,WAAW,KAAK,OAAO,EAAE;UAC3BkC,OAAO,CAACC,QAAQ,CAACD,OAAO,CAACE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC,MAAM,IAAIpC,WAAW,KAAK,MAAM,EAAE;UACjCkC,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAItC,WAAW,KAAK,KAAK,EAAE;UAChCkC,OAAO,CAACG,OAAO,CAACH,OAAO,CAACI,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;QACxC;QACA;MACF,KAAK,OAAO;QACVJ,OAAO,GAAG,IAAInC,IAAI,CAAC,CAAC;QACpB;MACF;QACE;IACJ;IAEAD,cAAc,CAACoC,OAAO,CAAC;EACzB,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAIC,IAAI,IAAK;IACjCvC,cAAc,CAACuC,IAAI,CAAC;EACtB,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGA,CAAC;IAAEC;EAAM,CAAC,kBAC9BxD,OAAA,CAAC1B,GAAG;IAACmF,EAAE,EAAE;MAAEhB,OAAO,EAAE,MAAM;MAAEiB,cAAc,EAAE,eAAe;MAAEC,UAAU,EAAE,QAAQ;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACzF7D,OAAA,CAAC1B,GAAG;MAACmF,EAAE,EAAE;QAAEhB,OAAO,EAAE,MAAM;QAAEqB,GAAG,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACnC7D,OAAA,CAAClB,MAAM;QAACiF,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,MAAM,CAAE;QAACkB,OAAO,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAAAJ,QAAA,EAAC;MAE/E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrE,OAAA,CAAClB,MAAM;QAACiF,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,OAAO,CAAE;QAACkB,OAAO,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAAAJ,QAAA,EAAC;MAEhF;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrE,OAAA,CAAClB,MAAM;QAACiF,OAAO,EAAEA,CAAA,KAAMjB,cAAc,CAAC,MAAM,CAAE;QAACkB,OAAO,EAAC,UAAU;QAACC,IAAI,EAAC,OAAO;QAAAJ,QAAA,EAAC;MAE/E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENrE,OAAA,CAACzB,UAAU;MAACyF,OAAO,EAAC,IAAI;MAACM,SAAS,EAAC,KAAK;MAAAT,QAAA,EACrCL;IAAK;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAEbrE,OAAA,CAAC1B,GAAG;MAACmF,EAAE,EAAE;QAAEhB,OAAO,EAAE,MAAM;QAAEqB,GAAG,EAAE;MAAE,CAAE;MAAAD,QAAA,gBACnC7D,OAAA,CAAClB,MAAM;QACLiF,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAAC,OAAO,CAAE;QACzCW,OAAO,EAAElD,WAAW,KAAK,OAAO,GAAG,WAAW,GAAG,UAAW;QAC5DmD,IAAI,EAAC,OAAO;QAAAJ,QAAA,EACb;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrE,OAAA,CAAClB,MAAM;QACLiF,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAAC,MAAM,CAAE;QACxCW,OAAO,EAAElD,WAAW,KAAK,MAAM,GAAG,WAAW,GAAG,UAAW;QAC3DmD,IAAI,EAAC,OAAO;QAAAJ,QAAA,EACb;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTrE,OAAA,CAAClB,MAAM;QACLiF,OAAO,EAAEA,CAAA,KAAMV,gBAAgB,CAAC,KAAK,CAAE;QACvCW,OAAO,EAAElD,WAAW,KAAK,KAAK,GAAG,WAAW,GAAG,UAAW;QAC1DmD,IAAI,EAAC,OAAO;QAAAJ,QAAA,EACb;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACErE,OAAA,CAAC1B,GAAG;IAACmF,EAAE,EAAE;MAAEc,MAAM,EAAE,OAAO;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAX,QAAA,gBACjC7D,OAAA,CAACxB,KAAK;MAACiF,EAAE,EAAE;QAAEe,CAAC,EAAE,CAAC;QAAED,MAAM,EAAE;MAAO,CAAE;MAAAV,QAAA,eAClC7D,OAAA,CAAC7B,QAAQ;QACP8B,SAAS,EAAEA,SAAU;QACrBe,MAAM,EAAEA,MAAO;QACfyD,aAAa,EAAC,OAAO;QACrBC,WAAW,EAAC,KAAK;QACjBtC,KAAK,EAAE;UAAEmC,MAAM,EAAE;QAAO,CAAE;QAC1BI,aAAa,EAAEjC,iBAAkB;QACjCkC,YAAY,EAAEjC,gBAAiB;QAC/BkC,UAAU;QACVC,eAAe,EAAE9C,gBAAiB;QAClC+C,UAAU,EAAE;UACVC,OAAO,EAAEzB;QACX,CAAE;QACF0B,KAAK,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAE;QAChCC,WAAW,EAAC,MAAM;QAClBC,IAAI,EAAE,EAAG;QACTC,SAAS,EAAE,CAAE;QACbC,GAAG,EAAE,IAAIxE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAE,CAAC;QAAA;QACjCyE,GAAG,EAAE,IAAIzE,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAE,CAAC;MAAA;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGRrE,OAAA,CAACtB,MAAM;MACL6G,IAAI,EAAE9E,eAAgB;MACtB+E,OAAO,EAAEA,CAAA,KAAM9E,kBAAkB,CAAC,KAAK,CAAE;MACzC+E,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAA7B,QAAA,gBAET7D,OAAA,CAACrB,WAAW;QAAC8E,EAAE,EAAE;UAAEhB,OAAO,EAAE,MAAM;UAAEiB,cAAc,EAAE,eAAe;UAAEC,UAAU,EAAE;QAAS,CAAE;QAAAE,QAAA,gBAC1F7D,OAAA,CAACzB,UAAU;UAACyF,OAAO,EAAC,IAAI;UAAAH,QAAA,EAAC;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACzDrE,OAAA,CAACd,UAAU;UAAC6E,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC,KAAK,CAAE;UAAAmD,QAAA,eACnD7D,OAAA,CAACH,SAAS;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,EAEb9D,aAAa,iBACZP,OAAA,CAACpB,aAAa;QAAAiF,QAAA,eACZ7D,OAAA,CAACjB,IAAI;UAAC4G,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA/B,QAAA,eACzB7D,OAAA,CAACjB,IAAI;YAAC8G,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAjC,QAAA,eAChB7D,OAAA,CAAChB,IAAI;cAACgF,OAAO,EAAC,UAAU;cAAAH,QAAA,eACtB7D,OAAA,CAACf,WAAW;gBAAA4E,QAAA,gBACV7D,OAAA,CAAC1B,GAAG;kBAACmF,EAAE,EAAE;oBAAEhB,OAAO,EAAE,MAAM;oBAAEkB,UAAU,EAAE,QAAQ;oBAAEC,EAAE,EAAE;kBAAE,CAAE;kBAAAC,QAAA,gBACxD7D,OAAA,CAACT,UAAU;oBAACkE,EAAE,EAAE;sBAAEsC,EAAE,EAAE,CAAC;sBAAExD,KAAK,EAAE;oBAAe;kBAAE;oBAAA2B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDrE,OAAA,CAACzB,UAAU;oBAACyF,OAAO,EAAC,IAAI;oBAAAH,QAAA,EAAEtD,aAAa,CAACoB;kBAAQ;oBAAAuC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eAC9DrE,OAAA,CAACvB,IAAI;oBACH+E,KAAK,EAAEjD,aAAa,CAAC4B,MAAO;oBAC5BI,KAAK,EAAEM,cAAc,CAACtC,aAAa,CAAC4B,MAAM,CAAE;oBAC5C8B,IAAI,EAAC,OAAO;oBACZR,EAAE,EAAE;sBAAEuC,EAAE,EAAE;oBAAO;kBAAE;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENrE,OAAA,CAACjB,IAAI;kBAAC4G,SAAS;kBAACC,OAAO,EAAE,CAAE;kBAAA/B,QAAA,gBACzB7D,OAAA,CAACjB,IAAI;oBAAC8G,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACG,EAAE,EAAE,CAAE;oBAAApC,QAAA,gBACvB7D,OAAA,CAAC1B,GAAG;sBAACmF,EAAE,EAAE;wBAAEhB,OAAO,EAAE,MAAM;wBAAEkB,UAAU,EAAE,QAAQ;wBAAEC,EAAE,EAAE;sBAAE,CAAE;sBAAAC,QAAA,gBACxD7D,OAAA,CAACX,SAAS;wBAACoE,EAAE,EAAE;0BAAEsC,EAAE,EAAE,CAAC;0BAAExD,KAAK,EAAE;wBAAiB;sBAAE;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACrDrE,OAAA,CAACzB,UAAU;wBAACyF,OAAO,EAAC,OAAO;wBAACzB,KAAK,EAAC,gBAAgB;wBAAAsB,QAAA,EAAC;sBAEnD;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrE,OAAA,CAACzB,UAAU;sBAACyF,OAAO,EAAC,OAAO;sBAAAH,QAAA,EAAEtD,aAAa,CAACqB;oBAAO;sBAAAsC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eAEPrE,OAAA,CAACjB,IAAI;oBAAC8G,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACG,EAAE,EAAE,CAAE;oBAAApC,QAAA,gBACvB7D,OAAA,CAAC1B,GAAG;sBAACmF,EAAE,EAAE;wBAAEhB,OAAO,EAAE,MAAM;wBAAEkB,UAAU,EAAE,QAAQ;wBAAEC,EAAE,EAAE;sBAAE,CAAE;sBAAAC,QAAA,gBACxD7D,OAAA,CAACT,UAAU;wBAACkE,EAAE,EAAE;0BAAEsC,EAAE,EAAE,CAAC;0BAAExD,KAAK,EAAE;wBAAiB;sBAAE;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtDrE,OAAA,CAACzB,UAAU;wBAACyF,OAAO,EAAC,OAAO;wBAACzB,KAAK,EAAC,gBAAgB;wBAAAsB,QAAA,EAAC;sBAEnD;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrE,OAAA,CAACzB,UAAU;sBAACyF,OAAO,EAAC,OAAO;sBAAAH,QAAA,EAAEtD,aAAa,CAAC2F;oBAAO;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D,CAAC,eAEPrE,OAAA,CAACjB,IAAI;oBAAC8G,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACG,EAAE,EAAE,CAAE;oBAAApC,QAAA,gBACvB7D,OAAA,CAAC1B,GAAG;sBAACmF,EAAE,EAAE;wBAAEhB,OAAO,EAAE,MAAM;wBAAEkB,UAAU,EAAE,QAAQ;wBAAEC,EAAE,EAAE;sBAAE,CAAE;sBAAAC,QAAA,gBACxD7D,OAAA,CAACP,YAAY;wBAACgE,EAAE,EAAE;0BAAEsC,EAAE,EAAE,CAAC;0BAAExD,KAAK,EAAE;wBAAiB;sBAAE;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACxDrE,OAAA,CAACzB,UAAU;wBAACyF,OAAO,EAAC,OAAO;wBAACzB,KAAK,EAAC,gBAAgB;wBAAAsB,QAAA,EAAC;sBAEnD;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrE,OAAA,CAACzB,UAAU;sBAACyF,OAAO,EAAC,OAAO;sBAAAH,QAAA,GACxBtD,aAAa,CAACa,IAAI,EAAC,MAAI,EAACb,aAAa,CAACc,IAAI;oBAAA;sBAAA6C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACbrE,OAAA,CAACzB,UAAU;sBAACyF,OAAO,EAAC,OAAO;sBAACzB,KAAK,EAAC,gBAAgB;sBAAAsB,QAAA,GAAC,YACvC,EAACtD,aAAa,CAACiB,QAAQ,EAAC,UACpC;oBAAA;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eAEPrE,OAAA,CAACjB,IAAI;oBAAC8G,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAACG,EAAE,EAAE,CAAE;oBAAApC,QAAA,gBACvB7D,OAAA,CAAC1B,GAAG;sBAACmF,EAAE,EAAE;wBAAEhB,OAAO,EAAE,MAAM;wBAAEkB,UAAU,EAAE,QAAQ;wBAAEC,EAAE,EAAE;sBAAE,CAAE;sBAAAC,QAAA,gBACxD7D,OAAA,CAACL,SAAS;wBAAC8D,EAAE,EAAE;0BAAEsC,EAAE,EAAE,CAAC;0BAAExD,KAAK,EAAE;wBAAiB;sBAAE;wBAAA2B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACrDrE,OAAA,CAACzB,UAAU;wBAACyF,OAAO,EAAC,OAAO;wBAACzB,KAAK,EAAC,gBAAgB;wBAAAsB,QAAA,EAAC;sBAEnD;wBAAAK,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNrE,OAAA,CAACzB,UAAU;sBAACyF,OAAO,EAAC,OAAO;sBAAAH,QAAA,GAAC,GAAC,EAACtD,aAAa,CAAC4F,KAAK;oBAAA;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3D,CAAC,eAEPrE,OAAA,CAACjB,IAAI;oBAAC8G,IAAI;oBAACC,EAAE,EAAE,EAAG;oBAAAjC,QAAA,eAChB7D,OAAA,CAACzB,UAAU;sBAACyF,OAAO,EAAC,OAAO;sBAACzB,KAAK,EAAC,gBAAgB;sBAAAsB,QAAA,GAAC,SAC1C,EAACtD,aAAa,CAAC6F,KAAK;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAChB,eAEDrE,OAAA,CAACnB,aAAa;QAAAgF,QAAA,eACZ7D,OAAA,CAAClB,MAAM;UAACiF,OAAO,EAAEA,CAAA,KAAMrD,kBAAkB,CAAC,KAAK,CAAE;UAAAmD,QAAA,EAAC;QAAK;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAChE,EAAA,CArSIH,YAAY;EAAA,QACSJ,UAAU;AAAA;AAAAuG,EAAA,GAD/BnG,YAAY;AAuSlB,eAAeA,YAAY;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}