{"ast": null, "code": "var baseFlatten = require('./_baseFlatten');\n\n/**\n * Flattens `array` a single level deep.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flatten([1, [2, [3, [4]], 5]]);\n * // => [1, 2, [3, [4]], 5]\n */\nfunction flatten(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, 1) : [];\n}\nmodule.exports = flatten;", "map": {"version": 3, "names": ["baseFlatten", "require", "flatten", "array", "length", "module", "exports"], "sources": ["D:/Project/salon-management-system/node_modules/lodash/flatten.js"], "sourcesContent": ["var baseFlatten = require('./_baseFlatten');\n\n/**\n * Flattens `array` a single level deep.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * _.flatten([1, [2, [3, [4]], 5]]);\n * // => [1, 2, [3, [4]], 5]\n */\nfunction flatten(array) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseFlatten(array, 1) : [];\n}\n\nmodule.exports = flatten;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,IAAIC,MAAM,GAAGD,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACC,MAAM;EAC7C,OAAOA,MAAM,GAAGJ,WAAW,CAACG,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE;AAC5C;AAEAE,MAAM,CAACC,OAAO,GAAGJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}