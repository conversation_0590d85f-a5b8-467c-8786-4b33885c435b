{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTablePaginationUtilityClass(slot) {\n  return generateUtilityClass('MuiTablePagination', slot);\n}\nconst tablePaginationClasses = generateUtilityClasses('MuiTablePagination', ['root', 'toolbar', 'spacer', 'selectLabel', 'selectRoot', 'select', 'selectIcon', 'input', 'menuItem', 'displayedRows', 'actions']);\nexport default tablePaginationClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTablePaginationUtilityClass", "slot", "tablePaginationClasses"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/TablePagination/tablePaginationClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTablePaginationUtilityClass(slot) {\n  return generateUtilityClass('MuiTablePagination', slot);\n}\nconst tablePaginationClasses = generateUtilityClasses('MuiTablePagination', ['root', 'toolbar', 'spacer', 'selectLabel', 'selectRoot', 'select', 'selectIcon', 'input', 'menuItem', 'displayedRows', 'actions']);\nexport default tablePaginationClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAE;EACnD,OAAOF,oBAAoB,CAAC,oBAAoB,EAAEE,IAAI,CAAC;AACzD;AACA,MAAMC,sBAAsB,GAAGJ,sBAAsB,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,EAAE,SAAS,CAAC,CAAC;AAChN,eAAeI,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}