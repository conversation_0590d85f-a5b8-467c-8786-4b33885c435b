{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Mock user data - In a real app, this would come from a backend\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst mockUsers = {\n  admin: {\n    id: 1,\n    username: 'admin',\n    password: 'admin123',\n    role: 'admin',\n    name: 'System Administrator',\n    email: '<EMAIL>',\n    permissions: ['all']\n  },\n  staff: [{\n    id: 2,\n    username: 'stylist1',\n    password: 'staff123',\n    role: 'staff',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    position: 'Senior Stylist',\n    permissions: ['appointments', 'customers', 'services']\n  }, {\n    id: 3,\n    username: 'receptionist1',\n    password: 'staff123',\n    role: 'staff',\n    name: 'Mike Chen',\n    email: '<EMAIL>',\n    position: 'Receptionist',\n    permissions: ['appointments', 'customers']\n  }],\n  customers: [{\n    id: 4,\n    username: 'customer1',\n    password: 'customer123',\n    role: 'customer',\n    name: 'Emma Wilson',\n    email: '<EMAIL>',\n    phone: '555-0123',\n    permissions: ['appointments']\n  }]\n};\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Check if user is logged in from localStorage\n    const savedUser = localStorage.getItem('salonUser');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n  const login = async (username, password, userType = 'staff') => {\n    try {\n      let foundUser = null;\n\n      // Trim whitespace from inputs\n      const trimmedUsername = username.trim();\n      const trimmedPassword = password.trim();\n      console.log('Login attempt:', {\n        username: trimmedUsername,\n        userType,\n        staffUsers: mockUsers.staff\n      });\n\n      // Check admin\n      if (userType === 'admin' && mockUsers.admin.username === trimmedUsername && mockUsers.admin.password === trimmedPassword) {\n        foundUser = mockUsers.admin;\n      }\n      // Check staff\n      else if (userType === 'staff') {\n        foundUser = mockUsers.staff.find(staff => staff.username === trimmedUsername && staff.password === trimmedPassword);\n        console.log('Staff search result:', foundUser);\n      }\n      // Check customers\n      else if (userType === 'customer') {\n        foundUser = mockUsers.customers.find(customer => customer.username === trimmedUsername && customer.password === trimmedPassword);\n      }\n      if (foundUser) {\n        const userWithoutPassword = {\n          ...foundUser\n        };\n        delete userWithoutPassword.password;\n        setUser(userWithoutPassword);\n        localStorage.setItem('salonUser', JSON.stringify(userWithoutPassword));\n        return {\n          success: true,\n          user: userWithoutPassword\n        };\n      } else {\n        return {\n          success: false,\n          error: 'Invalid credentials'\n        };\n      }\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Login failed'\n      };\n    }\n  };\n  const register = async userData => {\n    try {\n      // Generate new ID\n      const newId = Math.max(...mockUsers.customers.map(c => c.id)) + 1;\n\n      // Check if username already exists\n      const existingUser = mockUsers.customers.find(c => c.username === userData.username);\n      if (existingUser) {\n        return {\n          success: false,\n          error: 'Username already exists'\n        };\n      }\n\n      // Check if email already exists\n      const existingEmail = mockUsers.customers.find(c => c.email === userData.email);\n      if (existingEmail) {\n        return {\n          success: false,\n          error: 'Email already exists'\n        };\n      }\n      const newUser = {\n        id: newId,\n        username: userData.username,\n        password: userData.password,\n        role: 'customer',\n        name: userData.name,\n        email: userData.email,\n        phone: userData.phone,\n        permissions: ['appointments']\n      };\n\n      // Add to mock data (in real app, this would be an API call)\n      mockUsers.customers.push(newUser);\n      const userWithoutPassword = {\n        ...newUser\n      };\n      delete userWithoutPassword.password;\n      setUser(userWithoutPassword);\n      localStorage.setItem('salonUser', JSON.stringify(userWithoutPassword));\n      return {\n        success: true,\n        user: userWithoutPassword\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: 'Registration failed'\n      };\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('salonUser');\n  };\n  const hasPermission = permission => {\n    if (!user) return false;\n    if (user.role === 'admin') return true;\n    return user.permissions && user.permissions.includes(permission);\n  };\n  const isAdmin = () => user && user.role === 'admin';\n  const isStaff = () => user && user.role === 'staff';\n  const isCustomer = () => user && user.role === 'customer';\n  const value = {\n    user,\n    login,\n    register,\n    logout,\n    hasPermission,\n    isAdmin,\n    isStaff,\n    isCustomer,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 187,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "context", "Error", "mockUsers", "admin", "id", "username", "password", "role", "name", "email", "permissions", "staff", "position", "customers", "phone", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "savedUser", "localStorage", "getItem", "JSON", "parse", "login", "userType", "foundUser", "trimmedUsername", "trim", "trimmedPassword", "console", "log", "staffUsers", "find", "customer", "userWithoutPassword", "setItem", "stringify", "success", "error", "register", "userData", "newId", "Math", "max", "map", "c", "existingUser", "existingEmail", "newUser", "push", "logout", "removeItem", "hasPermission", "permission", "includes", "isAdmin", "isStaff", "isCustomer", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst AuthContext = createContext();\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\n// Mock user data - In a real app, this would come from a backend\nconst mockUsers = {\n  admin: {\n    id: 1,\n    username: 'admin',\n    password: 'admin123',\n    role: 'admin',\n    name: 'System Administrator',\n    email: '<EMAIL>',\n    permissions: ['all']\n  },\n  staff: [\n    {\n      id: 2,\n      username: 'stylist1',\n      password: 'staff123',\n      role: 'staff',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      position: 'Senior Stylist',\n      permissions: ['appointments', 'customers', 'services']\n    },\n    {\n      id: 3,\n      username: 'receptionist1',\n      password: 'staff123',\n      role: 'staff',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      position: 'Receptionist',\n      permissions: ['appointments', 'customers']\n    }\n  ],\n  customers: [\n    {\n      id: 4,\n      username: 'customer1',\n      password: 'customer123',\n      role: 'customer',\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      phone: '555-0123',\n      permissions: ['appointments']\n    }\n  ]\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is logged in from localStorage\n    const savedUser = localStorage.getItem('salonUser');\n    if (savedUser) {\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (username, password, userType = 'staff') => {\n    try {\n      let foundUser = null;\n\n      // Trim whitespace from inputs\n      const trimmedUsername = username.trim();\n      const trimmedPassword = password.trim();\n\n      console.log('Login attempt:', { username: trimmedUsername, userType, staffUsers: mockUsers.staff });\n\n      // Check admin\n      if (userType === 'admin' && mockUsers.admin.username === trimmedUsername && mockUsers.admin.password === trimmedPassword) {\n        foundUser = mockUsers.admin;\n      }\n      // Check staff\n      else if (userType === 'staff') {\n        foundUser = mockUsers.staff.find(staff =>\n          staff.username === trimmedUsername && staff.password === trimmedPassword\n        );\n        console.log('Staff search result:', foundUser);\n      }\n      // Check customers\n      else if (userType === 'customer') {\n        foundUser = mockUsers.customers.find(customer =>\n          customer.username === trimmedUsername && customer.password === trimmedPassword\n        );\n      }\n\n      if (foundUser) {\n        const userWithoutPassword = { ...foundUser };\n        delete userWithoutPassword.password;\n        \n        setUser(userWithoutPassword);\n        localStorage.setItem('salonUser', JSON.stringify(userWithoutPassword));\n        return { success: true, user: userWithoutPassword };\n      } else {\n        return { success: false, error: 'Invalid credentials' };\n      }\n    } catch (error) {\n      return { success: false, error: 'Login failed' };\n    }\n  };\n\n  const register = async (userData) => {\n    try {\n      // Generate new ID\n      const newId = Math.max(...mockUsers.customers.map(c => c.id)) + 1;\n      \n      // Check if username already exists\n      const existingUser = mockUsers.customers.find(c => c.username === userData.username);\n      if (existingUser) {\n        return { success: false, error: 'Username already exists' };\n      }\n\n      // Check if email already exists\n      const existingEmail = mockUsers.customers.find(c => c.email === userData.email);\n      if (existingEmail) {\n        return { success: false, error: 'Email already exists' };\n      }\n\n      const newUser = {\n        id: newId,\n        username: userData.username,\n        password: userData.password,\n        role: 'customer',\n        name: userData.name,\n        email: userData.email,\n        phone: userData.phone,\n        permissions: ['appointments']\n      };\n\n      // Add to mock data (in real app, this would be an API call)\n      mockUsers.customers.push(newUser);\n\n      const userWithoutPassword = { ...newUser };\n      delete userWithoutPassword.password;\n\n      setUser(userWithoutPassword);\n      localStorage.setItem('salonUser', JSON.stringify(userWithoutPassword));\n      \n      return { success: true, user: userWithoutPassword };\n    } catch (error) {\n      return { success: false, error: 'Registration failed' };\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    localStorage.removeItem('salonUser');\n  };\n\n  const hasPermission = (permission) => {\n    if (!user) return false;\n    if (user.role === 'admin') return true;\n    return user.permissions && user.permissions.includes(permission);\n  };\n\n  const isAdmin = () => user && user.role === 'admin';\n  const isStaff = () => user && user.role === 'staff';\n  const isCustomer = () => user && user.role === 'customer';\n\n  const value = {\n    user,\n    login,\n    register,\n    logout,\n    hasPermission,\n    isAdmin,\n    isStaff,\n    isCustomer,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMO,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGR,UAAU,CAACK,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;;AAED;AAAAD,EAAA,CARaD,OAAO;AASpB,MAAMI,SAAS,GAAG;EAChBC,KAAK,EAAE;IACLC,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,OAAO;IACjBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,sBAAsB;IAC5BC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,CAAC,KAAK;EACrB,CAAC;EACDC,KAAK,EAAE,CACL;IACEP,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,iBAAiB;IACxBG,QAAQ,EAAE,gBAAgB;IAC1BF,WAAW,EAAE,CAAC,cAAc,EAAE,WAAW,EAAE,UAAU;EACvD,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,eAAe;IACzBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,gBAAgB;IACvBG,QAAQ,EAAE,cAAc;IACxBF,WAAW,EAAE,CAAC,cAAc,EAAE,WAAW;EAC3C,CAAC,CACF;EACDG,SAAS,EAAE,CACT;IACET,EAAE,EAAE,CAAC;IACLC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE,gBAAgB;IACvBK,KAAK,EAAE,UAAU;IACjBJ,WAAW,EAAE,CAAC,cAAc;EAC9B,CAAC;AAEL,CAAC;AAED,OAAO,MAAMK,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAM4B,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;IACnD,IAAIF,SAAS,EAAE;MACbH,OAAO,CAACM,IAAI,CAACC,KAAK,CAACJ,SAAS,CAAC,CAAC;IAChC;IACAD,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,KAAK,GAAG,MAAAA,CAAOtB,QAAQ,EAAEC,QAAQ,EAAEsB,QAAQ,GAAG,OAAO,KAAK;IAC9D,IAAI;MACF,IAAIC,SAAS,GAAG,IAAI;;MAEpB;MACA,MAAMC,eAAe,GAAGzB,QAAQ,CAAC0B,IAAI,CAAC,CAAC;MACvC,MAAMC,eAAe,GAAG1B,QAAQ,CAACyB,IAAI,CAAC,CAAC;MAEvCE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE;QAAE7B,QAAQ,EAAEyB,eAAe;QAAEF,QAAQ;QAAEO,UAAU,EAAEjC,SAAS,CAACS;MAAM,CAAC,CAAC;;MAEnG;MACA,IAAIiB,QAAQ,KAAK,OAAO,IAAI1B,SAAS,CAACC,KAAK,CAACE,QAAQ,KAAKyB,eAAe,IAAI5B,SAAS,CAACC,KAAK,CAACG,QAAQ,KAAK0B,eAAe,EAAE;QACxHH,SAAS,GAAG3B,SAAS,CAACC,KAAK;MAC7B;MACA;MAAA,KACK,IAAIyB,QAAQ,KAAK,OAAO,EAAE;QAC7BC,SAAS,GAAG3B,SAAS,CAACS,KAAK,CAACyB,IAAI,CAACzB,KAAK,IACpCA,KAAK,CAACN,QAAQ,KAAKyB,eAAe,IAAInB,KAAK,CAACL,QAAQ,KAAK0B,eAC3D,CAAC;QACDC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEL,SAAS,CAAC;MAChD;MACA;MAAA,KACK,IAAID,QAAQ,KAAK,UAAU,EAAE;QAChCC,SAAS,GAAG3B,SAAS,CAACW,SAAS,CAACuB,IAAI,CAACC,QAAQ,IAC3CA,QAAQ,CAAChC,QAAQ,KAAKyB,eAAe,IAAIO,QAAQ,CAAC/B,QAAQ,KAAK0B,eACjE,CAAC;MACH;MAEA,IAAIH,SAAS,EAAE;QACb,MAAMS,mBAAmB,GAAG;UAAE,GAAGT;QAAU,CAAC;QAC5C,OAAOS,mBAAmB,CAAChC,QAAQ;QAEnCa,OAAO,CAACmB,mBAAmB,CAAC;QAC5Bf,YAAY,CAACgB,OAAO,CAAC,WAAW,EAAEd,IAAI,CAACe,SAAS,CAACF,mBAAmB,CAAC,CAAC;QACtE,OAAO;UAAEG,OAAO,EAAE,IAAI;UAAEvB,IAAI,EAAEoB;QAAoB,CAAC;MACrD,CAAC,MAAM;QACL,OAAO;UAAEG,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAsB,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAe,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,QAAQ,GAAG,MAAOC,QAAQ,IAAK;IACnC,IAAI;MACF;MACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG7C,SAAS,CAACW,SAAS,CAACmC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAAC7C,EAAE,CAAC,CAAC,GAAG,CAAC;;MAEjE;MACA,MAAM8C,YAAY,GAAGhD,SAAS,CAACW,SAAS,CAACuB,IAAI,CAACa,CAAC,IAAIA,CAAC,CAAC5C,QAAQ,KAAKuC,QAAQ,CAACvC,QAAQ,CAAC;MACpF,IAAI6C,YAAY,EAAE;QAChB,OAAO;UAAET,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAE;QAA0B,CAAC;MAC7D;;MAEA;MACA,MAAMS,aAAa,GAAGjD,SAAS,CAACW,SAAS,CAACuB,IAAI,CAACa,CAAC,IAAIA,CAAC,CAACxC,KAAK,KAAKmC,QAAQ,CAACnC,KAAK,CAAC;MAC/E,IAAI0C,aAAa,EAAE;QACjB,OAAO;UAAEV,OAAO,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAuB,CAAC;MAC1D;MAEA,MAAMU,OAAO,GAAG;QACdhD,EAAE,EAAEyC,KAAK;QACTxC,QAAQ,EAAEuC,QAAQ,CAACvC,QAAQ;QAC3BC,QAAQ,EAAEsC,QAAQ,CAACtC,QAAQ;QAC3BC,IAAI,EAAE,UAAU;QAChBC,IAAI,EAAEoC,QAAQ,CAACpC,IAAI;QACnBC,KAAK,EAAEmC,QAAQ,CAACnC,KAAK;QACrBK,KAAK,EAAE8B,QAAQ,CAAC9B,KAAK;QACrBJ,WAAW,EAAE,CAAC,cAAc;MAC9B,CAAC;;MAED;MACAR,SAAS,CAACW,SAAS,CAACwC,IAAI,CAACD,OAAO,CAAC;MAEjC,MAAMd,mBAAmB,GAAG;QAAE,GAAGc;MAAQ,CAAC;MAC1C,OAAOd,mBAAmB,CAAChC,QAAQ;MAEnCa,OAAO,CAACmB,mBAAmB,CAAC;MAC5Bf,YAAY,CAACgB,OAAO,CAAC,WAAW,EAAEd,IAAI,CAACe,SAAS,CAACF,mBAAmB,CAAC,CAAC;MAEtE,OAAO;QAAEG,OAAO,EAAE,IAAI;QAAEvB,IAAI,EAAEoB;MAAoB,CAAC;IACrD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACd,OAAO;QAAED,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAE;MAAsB,CAAC;IACzD;EACF,CAAC;EAED,MAAMY,MAAM,GAAGA,CAAA,KAAM;IACnBnC,OAAO,CAAC,IAAI,CAAC;IACbI,YAAY,CAACgC,UAAU,CAAC,WAAW,CAAC;EACtC,CAAC;EAED,MAAMC,aAAa,GAAIC,UAAU,IAAK;IACpC,IAAI,CAACvC,IAAI,EAAE,OAAO,KAAK;IACvB,IAAIA,IAAI,CAACX,IAAI,KAAK,OAAO,EAAE,OAAO,IAAI;IACtC,OAAOW,IAAI,CAACR,WAAW,IAAIQ,IAAI,CAACR,WAAW,CAACgD,QAAQ,CAACD,UAAU,CAAC;EAClE,CAAC;EAED,MAAME,OAAO,GAAGA,CAAA,KAAMzC,IAAI,IAAIA,IAAI,CAACX,IAAI,KAAK,OAAO;EACnD,MAAMqD,OAAO,GAAGA,CAAA,KAAM1C,IAAI,IAAIA,IAAI,CAACX,IAAI,KAAK,OAAO;EACnD,MAAMsD,UAAU,GAAGA,CAAA,KAAM3C,IAAI,IAAIA,IAAI,CAACX,IAAI,KAAK,UAAU;EAEzD,MAAMuD,KAAK,GAAG;IACZ5C,IAAI;IACJS,KAAK;IACLgB,QAAQ;IACRW,MAAM;IACNE,aAAa;IACbG,OAAO;IACPC,OAAO;IACPC,UAAU;IACVzC;EACF,CAAC;EAED,oBACExB,OAAA,CAACC,WAAW,CAACkE,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA9C,QAAA,EAChCA;EAAQ;IAAAgD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAClD,GAAA,CAnIWF,YAAY;AAAAqD,EAAA,GAAZrD,YAAY;AAAA,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}