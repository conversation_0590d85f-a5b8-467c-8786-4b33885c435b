import React from 'react';
import {
  <PERSON>rid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  LinearProgress,
  Alert,
  Button,
} from '@mui/material';
import {
  TrendingUp,
  People,
  Event,
  AttachMoney,
  Schedule,
  Person,
  CalendarToday,
  PersonAdd,
  Visibility,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => {
  const { user, isAdmin, isStaff, isCustomer } = useAuth();
  const navigate = useNavigate();

  // Role-based stats - different data based on user role
  const getStatsForRole = () => {
    if (isAdmin()) {
      return [
        {
          title: 'Today\'s Revenue',
          value: '$1,250',
          icon: <AttachMoney />,
          color: '#4caf50',
          change: '+12%',
        },
        {
          title: 'Appointments Today',
          value: '24',
          icon: <Event />,
          color: '#2196f3',
          change: '+5%',
        },
        {
          title: 'Total Customers',
          value: '1,847',
          icon: <People />,
          color: '#ff9800',
          change: '+8%',
        },
        {
          title: 'Staff Working',
          value: '8',
          icon: <Person />,
          color: '#9c27b0',
          change: '0%',
        },
      ];
    } else if (isStaff()) {
      return [
        {
          title: 'My Appointments Today',
          value: '8',
          icon: <Event />,
          color: '#2196f3',
          change: '+2',
        },
        {
          title: 'Completed Today',
          value: '3',
          icon: <Schedule />,
          color: '#4caf50',
          change: '+1',
        },
        {
          title: 'My Customers',
          value: '156',
          icon: <People />,
          color: '#ff9800',
          change: '+5',
        },
        {
          title: 'Today\'s Earnings',
          value: '$320',
          icon: <AttachMoney />,
          color: '#9c27b0',
          change: '+15%',
        },
      ];
    } else {
      // Customer view
      return [
        {
          title: 'Upcoming Appointments',
          value: '2',
          icon: <Event />,
          color: '#2196f3',
          change: 'Next: Tomorrow',
        },
        {
          title: 'Total Visits',
          value: '12',
          icon: <Schedule />,
          color: '#4caf50',
          change: 'This year',
        },
        {
          title: 'Favorite Services',
          value: '3',
          icon: <Person />,
          color: '#ff9800',
          change: 'Hair & Color',
        },
        {
          title: 'Loyalty Points',
          value: '450',
          icon: <AttachMoney />,
          color: '#9c27b0',
          change: '50 to reward',
        },
      ];
    }
  };

  const stats = getStatsForRole();

  const todayAppointments = [
    {
      id: 1,
      customer: 'Sarah Johnson',
      service: 'Hair Cut & Style',
      time: '9:00 AM',
      status: 'completed',
      stylist: 'Emma Wilson',
    },
    {
      id: 2,
      customer: 'Mike Davis',
      service: 'Beard Trim',
      time: '10:30 AM',
      status: 'in-progress',
      stylist: 'John Smith',
    },
    {
      id: 3,
      customer: 'Lisa Brown',
      service: 'Hair Color',
      time: '11:00 AM',
      status: 'scheduled',
      stylist: 'Emma Wilson',
    },
    {
      id: 4,
      customer: 'Tom Wilson',
      service: 'Full Service',
      time: '2:00 PM',
      status: 'scheduled',
      stylist: 'Mike Johnson',
    },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'scheduled':
        return 'primary';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in-progress':
        return 'In Progress';
      case 'scheduled':
        return 'Scheduled';
      default:
        return status;
    }
  };

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          {isCustomer() ? `Welcome back, ${user?.name}!` : 'Dashboard'}
        </Typography>
        {isCustomer() && (
          <Button
            variant="contained"
            startIcon={<CalendarToday />}
            onClick={() => navigate('/appointments')}
          >
            Book Appointment
          </Button>
        )}
      </Box>

      {/* Welcome message for customers */}
      {isCustomer() && (
        <Alert severity="info" sx={{ mb: 3 }}>
          Welcome to your personal dashboard! Here you can view your appointments, track your visits, and manage your profile.
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar sx={{ bgcolor: stat.color, mr: 2 }}>
                    {stat.icon}
                  </Avatar>
                  <Box>
                    <Typography variant="h4" component="div">
                      {stat.value}
                    </Typography>
                    <Typography color="text.secondary" variant="body2">
                      {stat.title}
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <TrendingUp sx={{ color: '#4caf50', mr: 1 }} />
                  <Typography variant="body2" color="success.main">
                    {stat.change} {isCustomer() ? '' : 'from yesterday'}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        {/* Appointments Section - Different content based on role */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              {isCustomer() ? 'Your Upcoming Appointments' :
               isStaff() ? 'My Today\'s Appointments' : 'Today\'s Appointments'}
            </Typography>
            {isCustomer() ? (
              <List>
                <ListItem divider>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <Schedule />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          Hair Cut & Color
                        </Typography>
                        <Chip label="Tomorrow" color="primary" size="small" />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          10:00 AM - 12:00 PM
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Stylist: Emma Wilson
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                <ListItem>
                  <ListItemAvatar>
                    <Avatar sx={{ bgcolor: 'secondary.main' }}>
                      <Schedule />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          Manicure
                        </Typography>
                        <Chip label="Next Week" color="secondary" size="small" />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          Friday, 2:00 PM
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Nail Technician: Lisa Brown
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              </List>
            ) : (
              <List>
                {todayAppointments.map((appointment) => (
                  <ListItem key={appointment.id} divider>
                    <ListItemAvatar>
                      <Avatar>
                        <Schedule />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle1">
                            {appointment.customer}
                          </Typography>
                          <Chip
                            label={getStatusText(appointment.status)}
                            color={getStatusColor(appointment.status)}
                            size="small"
                          />
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {appointment.service} • {appointment.time}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Stylist: {appointment.stylist}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Right Sidebar - Role-based content */}
        <Grid item xs={12} md={4}>
          {!isCustomer() && (
            <Paper sx={{ p: 3, mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                {isAdmin() ? "Today's Progress" : "My Progress"}
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Appointments Completed
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={isStaff() ? 37 : 25}
                  sx={{ mt: 1, mb: 1 }}
                />
                <Typography variant="body2">
                  {isStaff() ? '3 of 8 completed' : '6 of 24 completed'}
                </Typography>
              </Box>
              {isAdmin() && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Revenue Target
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={62}
                    sx={{ mt: 1, mb: 1 }}
                    color="success"
                  />
                  <Typography variant="body2">
                    $1,250 of $2,000 target
                  </Typography>
                </Box>
              )}
            </Paper>
          )}

          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {isCustomer() ? (
                <>
                  <Button
                    variant="outlined"
                    startIcon={<CalendarToday />}
                    onClick={() => navigate('/appointments')}
                    fullWidth
                  >
                    Book Appointment
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Visibility />}
                    onClick={() => navigate('/appointments')}
                    fullWidth
                  >
                    View My Appointments
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Person />}
                    onClick={() => navigate('/profile')}
                    fullWidth
                  >
                    Edit Profile
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    variant="outlined"
                    startIcon={<CalendarToday />}
                    onClick={() => navigate('/appointments')}
                    fullWidth
                  >
                    New Appointment
                  </Button>
                  {(isAdmin() || (isStaff() && user?.permissions?.includes('customers'))) && (
                    <Button
                      variant="outlined"
                      startIcon={<PersonAdd />}
                      onClick={() => navigate('/customers')}
                      fullWidth
                    >
                      Add Customer
                    </Button>
                  )}
                  <Button
                    variant="outlined"
                    startIcon={<Visibility />}
                    onClick={() => navigate('/appointments')}
                    fullWidth
                  >
                    View Schedule
                  </Button>
                </>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
