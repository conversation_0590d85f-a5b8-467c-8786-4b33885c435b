{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n * @type {React.Context<{} | {expanded: boolean, disabled: boolean, toggle: () => void}>}\n */\nconst ImageListContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ImageListContext.displayName = 'ImageListContext';\n}\nexport default ImageListContext;", "map": {"version": 3, "names": ["React", "ImageListContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["D:/Project/salon-management-system/node_modules/@mui/material/esm/ImageList/ImageListContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n\n/**\n * @ignore - internal component.\n * @type {React.Context<{} | {expanded: boolean, disabled: boolean, toggle: () => void}>}\n */\nconst ImageListContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  ImageListContext.displayName = 'ImageListContext';\n}\nexport default ImageListContext;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;AAC7D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,gBAAgB,CAACK,WAAW,GAAG,kBAAkB;AACnD;AACA,eAAeL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}