{"ast": null, "code": "var _jsxFileName = \"D:\\\\Project\\\\salon-management-system\\\\src\\\\App.js\";\nimport React from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport Dashboard from './components/Dashboard';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#8e24aa'\n    },\n    secondary: {\n      main: '#ff4081'\n    },\n    background: {\n      default: '#f5f5f5'\n    }\n  }\n});\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(\"main\", {\n        style: {\n          padding: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "ThemeProvider", "createTheme", "CssBaseline", "Dashboard", "jsxDEV", "_jsxDEV", "theme", "palette", "primary", "main", "secondary", "background", "default", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "style", "padding", "_c", "$RefreshReg$"], "sources": ["D:/Project/salon-management-system/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport Dashboard from './components/Dashboard';\nimport './App.css';\n\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#8e24aa',\n    },\n    secondary: {\n      main: '#ff4081',\n    },\n    background: {\n      default: '#f5f5f5',\n    },\n  },\n});\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <div className=\"App\">\n        <main style={{ padding: '20px' }}>\n          <Dashboard />\n        </main>\n      </div>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,KAAK,GAAGL,WAAW,CAAC;EACxBM,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTD,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE;IACX;EACF;AACF,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EACb,oBACER,OAAA,CAACL,aAAa;IAACM,KAAK,EAAEA,KAAM;IAAAQ,QAAA,gBAC1BT,OAAA,CAACH,WAAW;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfb,OAAA;MAAKc,SAAS,EAAC,KAAK;MAAAL,QAAA,eAClBT,OAAA;QAAMe,KAAK,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAE;QAAAP,QAAA,eAC/BT,OAAA,CAACF,SAAS;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACI,EAAA,GAXQT,GAAG;AAaZ,eAAeA,GAAG;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}