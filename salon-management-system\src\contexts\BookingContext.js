import React, { createContext, useContext, useState, useCallback } from 'react';
import { format, addMinutes, isAfter, isBefore, isSameDay, parseISO } from 'date-fns';

const BookingContext = createContext();

export const useBooking = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a BookingProvider');
  }
  return context;
};

// Mock data for services
const mockServices = [
  {
    id: 1,
    name: 'Hair Cut & Style',
    category: 'Hair',
    price: 85,
    duration: 60,
    description: 'Professional haircut with styling',
  },
  {
    id: 2,
    name: 'Hair Color',
    category: 'Hair',
    price: 150,
    duration: 120,
    description: 'Full hair coloring service',
  },
  {
    id: 3,
    name: 'Beard Trim',
    category: 'Grooming',
    price: 35,
    duration: 30,
    description: 'Professional beard trimming and shaping',
  },
  {
    id: 4,
    name: 'Full Service',
    category: 'Complete',
    price: 120,
    duration: 90,
    description: 'Complete hair and grooming service',
  },
  {
    id: 5,
    name: 'Manicure',
    category: 'Nails',
    price: 45,
    duration: 45,
    description: 'Professional nail care and polish',
  },
  {
    id: 6,
    name: 'Pedicure',
    category: 'Nails',
    price: 55,
    duration: 60,
    description: 'Foot care and nail treatment',
  },
];

// Mock data for stylists
const mockStylists = [
  {
    id: 1,
    name: 'Emma Wilson',
    specialties: ['Hair Cut', 'Hair Color', 'Styling'],
    workingHours: { start: '09:00', end: '18:00' },
    workingDays: [1, 2, 3, 4, 5], // Monday to Friday
    rating: 4.9,
  },
  {
    id: 2,
    name: 'John Smith',
    specialties: ['Hair Cut', 'Beard Trim', 'Grooming'],
    workingHours: { start: '10:00', end: '19:00' },
    workingDays: [1, 2, 3, 4, 5, 6], // Monday to Saturday
    rating: 4.7,
  },
  {
    id: 3,
    name: 'Mike Johnson',
    specialties: ['Full Service', 'Hair Cut', 'Styling'],
    workingHours: { start: '08:00', end: '17:00' },
    workingDays: [1, 2, 3, 4, 5], // Monday to Friday
    rating: 4.8,
  },
  {
    id: 4,
    name: 'Sarah Davis',
    specialties: ['Manicure', 'Pedicure', 'Nail Art'],
    workingHours: { start: '09:00', end: '18:00' },
    workingDays: [2, 3, 4, 5, 6], // Tuesday to Saturday
    rating: 4.9,
  },
];

export const BookingProvider = ({ children }) => {
  const [bookingStep, setBookingStep] = useState(0); // 0: service, 1: stylist, 2: datetime, 3: confirmation
  const [selectedService, setSelectedService] = useState(null);
  const [selectedStylist, setStylist] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedTime, setSelectedTime] = useState(null);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    email: '',
    notes: '',
  });
  const [appointments, setAppointments] = useState([
    {
      id: 1,
      customer: 'Sarah Johnson',
      phone: '(*************',
      service: 'Hair Cut & Style',
      serviceId: 1,
      stylist: 'Emma Wilson',
      stylistId: 1,
      date: '2024-07-16',
      time: '09:00',
      duration: 60,
      price: 85,
      status: 'completed',
    },
    {
      id: 2,
      customer: 'Mike Davis',
      phone: '(*************',
      service: 'Beard Trim',
      serviceId: 3,
      stylist: 'John Smith',
      stylistId: 2,
      date: '2024-07-16',
      time: '10:30',
      duration: 30,
      price: 35,
      status: 'in-progress',
    },
    {
      id: 3,
      customer: 'Lisa Brown',
      phone: '(*************',
      service: 'Hair Color',
      serviceId: 2,
      stylist: 'Emma Wilson',
      stylistId: 1,
      date: '2024-07-17',
      time: '11:00',
      duration: 120,
      price: 150,
      status: 'scheduled',
    },
    {
      id: 4,
      customer: 'Tom Wilson',
      phone: '(*************',
      service: 'Full Service',
      serviceId: 4,
      stylist: 'Mike Johnson',
      stylistId: 3,
      date: '2024-07-18',
      time: '14:00',
      duration: 90,
      price: 120,
      status: 'scheduled',
    },
    {
      id: 5,
      customer: 'Emily Chen',
      phone: '(*************',
      service: 'Manicure',
      serviceId: 5,
      stylist: 'Sarah Davis',
      stylistId: 4,
      date: '2024-07-16',
      time: '15:30',
      duration: 45,
      price: 45,
      status: 'scheduled',
    },
    {
      id: 6,
      customer: 'David Brown',
      phone: '(*************',
      service: 'Hair Cut & Style',
      serviceId: 1,
      stylist: 'John Smith',
      stylistId: 2,
      date: '2024-07-19',
      time: '10:00',
      duration: 60,
      price: 85,
      status: 'scheduled',
    },
    {
      id: 7,
      customer: 'Anna Martinez',
      phone: '(*************',
      service: 'Pedicure',
      serviceId: 6,
      stylist: 'Sarah Davis',
      stylistId: 4,
      date: '2024-07-17',
      time: '13:00',
      duration: 60,
      price: 55,
      status: 'scheduled',
    },
  ]);

  // Get available stylists for a service
  const getAvailableStylists = useCallback((serviceId) => {
    const service = mockServices.find(s => s.id === serviceId);
    if (!service) return [];

    return mockStylists.filter(stylist => 
      stylist.specialties.some(specialty => 
        service.name.toLowerCase().includes(specialty.toLowerCase()) ||
        service.category.toLowerCase().includes(specialty.toLowerCase())
      )
    );
  }, []);

  // Generate time slots for a stylist on a specific date
  const getAvailableTimeSlots = useCallback((stylistId, date, serviceDuration = 60) => {
    const stylist = mockStylists.find(s => s.id === stylistId);
    if (!stylist) return [];

    const selectedDate = new Date(date);
    const dayOfWeek = selectedDate.getDay();
    
    // Check if stylist works on this day
    if (!stylist.workingDays.includes(dayOfWeek)) return [];

    const slots = [];
    const startTime = stylist.workingHours.start;
    const endTime = stylist.workingHours.end;
    
    // Parse working hours
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);
    
    const startDateTime = new Date(selectedDate);
    startDateTime.setHours(startHour, startMinute, 0, 0);
    
    const endDateTime = new Date(selectedDate);
    endDateTime.setHours(endHour, endMinute, 0, 0);

    // Generate 30-minute slots
    let currentTime = new Date(startDateTime);
    
    while (currentTime < endDateTime) {
      const slotEndTime = addMinutes(currentTime, serviceDuration);
      
      // Check if slot fits within working hours
      if (slotEndTime <= endDateTime) {
        const timeString = format(currentTime, 'HH:mm');
        
        // Check if slot conflicts with existing appointments
        const hasConflict = appointments.some(apt => {
          if (apt.stylistId !== stylistId || apt.date !== format(selectedDate, 'yyyy-MM-dd')) {
            return false;
          }
          
          const aptStart = new Date(`${apt.date}T${apt.time}`);
          const aptEnd = addMinutes(aptStart, apt.duration);
          
          return (
            (currentTime >= aptStart && currentTime < aptEnd) ||
            (slotEndTime > aptStart && slotEndTime <= aptEnd) ||
            (currentTime <= aptStart && slotEndTime >= aptEnd)
          );
        });
        
        if (!hasConflict) {
          slots.push({
            time: timeString,
            available: true,
          });
        }
      }
      
      currentTime = addMinutes(currentTime, 30); // 30-minute intervals
    }
    
    return slots;
  }, [appointments]);

  // Reset booking state
  const resetBooking = useCallback(() => {
    setBookingStep(0);
    setSelectedService(null);
    setStylist(null);
    setSelectedDate(null);
    setSelectedTime(null);
    setCustomerInfo({
      name: '',
      phone: '',
      email: '',
      notes: '',
    });
  }, []);

  // Create new appointment
  const createAppointment = useCallback((appointmentData) => {
    const newAppointment = {
      id: Math.max(...appointments.map(a => a.id)) + 1,
      ...appointmentData,
      status: 'scheduled',
    };
    
    setAppointments(prev => [...prev, newAppointment]);
    return newAppointment;
  }, [appointments]);

  // Cancel appointment
  const cancelAppointment = useCallback((appointmentId) => {
    setAppointments(prev => 
      prev.map(apt => 
        apt.id === appointmentId 
          ? { ...apt, status: 'cancelled' }
          : apt
      )
    );
  }, []);

  const value = {
    // Booking state
    bookingStep,
    setBookingStep,
    selectedService,
    setSelectedService,
    selectedStylist,
    setStylist,
    selectedDate,
    setSelectedDate,
    selectedTime,
    setSelectedTime,
    customerInfo,
    setCustomerInfo,
    
    // Data
    services: mockServices,
    stylists: mockStylists,
    appointments,
    setAppointments,
    
    // Functions
    getAvailableStylists,
    getAvailableTimeSlots,
    resetBooking,
    createAppointment,
    cancelAppointment,
  };

  return (
    <BookingContext.Provider value={value}>
      {children}
    </BookingContext.Provider>
  );
};
